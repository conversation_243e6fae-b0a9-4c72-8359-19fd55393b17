#!/usr/bin/env python3
"""
模型下载脚本
专门用于下载EasyOCR和翻译模型到项目目录
"""

import os
import sys
import requests
import threading
from pathlib import Path
import time

# 设置模型目录
MODEL_DIR = Path("models")
MODEL_DIR.mkdir(exist_ok=True)

# EasyOCR模型下载链接
EASYOCR_MODELS = {
    "craft_mlt_25k.pth": "https://github.com/JaidedAI/EasyOCR/releases/download/v1.3.2/craft_mlt_25k.pth",
    "latin_g2.pth": "https://github.com/JaidedAI/EasyOCR/releases/download/v1.6.2/latin_g2.pth",
    "chinese_g2.pth": "https://github.com/JaidedAI/EasyOCR/releases/download/v1.6.2/chinese_g2.pth",
    "english_g2.pth": "https://github.com/JaidedAI/EasyOCR/releases/download/v1.6.2/english_g2.pth"
}

def download_file(url, filename, description=""):
    """下载单个文件，显示进度"""
    filepath = MODEL_DIR / filename
    
    if filepath.exists():
        print(f"✅ {filename} 已存在，跳过下载")
        return True
    
    print(f"🔄 开始下载 {description or filename}...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📥 {filename}: {progress:.1f}% ({downloaded//1024//1024}MB/{total_size//1024//1024}MB)", end="")
        
        print(f"\n✅ {filename} 下载完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ {filename} 下载失败: {e}")
        if filepath.exists():
            filepath.unlink()  # 删除不完整的文件
        return False

def download_easyocr_models():
    """下载EasyOCR模型"""
    print("=" * 50)
    print("📦 开始下载 EasyOCR 模型")
    print("=" * 50)
    
    # 创建EasyOCR模型子目录
    easyocr_dir = MODEL_DIR / "easyocr"
    easyocr_dir.mkdir(exist_ok=True)
    
    success_count = 0
    total_count = len(EASYOCR_MODELS)
    
    for filename, url in EASYOCR_MODELS.items():
        if download_file(url, f"easyocr/{filename}", f"EasyOCR {filename}"):
            success_count += 1
    
    print(f"\n📊 EasyOCR模型下载完成: {success_count}/{total_count}")
    return success_count == total_count

def download_translation_model():
    """下载翻译模型 - 使用Hugging Face Hub"""
    print("=" * 50)
    print("📦 开始下载 翻译模型")
    print("=" * 50)

    try:
        # 设置环境变量
        os.environ['TRANSFORMERS_CACHE'] = str(MODEL_DIR)
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

        from transformers import MarianMTModel, MarianTokenizer

        model_name = "Helsinki-NLP/opus-mt-zh-en"
        print(f"🔄 下载翻译模型: {model_name}")

        # 下载tokenizer
        print("📥 下载 tokenizer...")
        tokenizer = MarianTokenizer.from_pretrained(
            model_name,
            cache_dir=MODEL_DIR,
            trust_remote_code=True
        )

        # 下载模型，使用safetensors格式
        print("📥 下载 model...")
        model = MarianMTModel.from_pretrained(
            model_name,
            cache_dir=MODEL_DIR,
            trust_remote_code=True,
            use_safetensors=True
        )

        print("✅ 翻译模型下载完成!")
        return True

    except Exception as e:
        print(f"❌ 翻译模型下载失败: {e}")
        print("💡 尝试使用镜像版本...")
        return False

def setup_easyocr_path():
    """设置EasyOCR模型路径"""
    try:
        # 创建符合EasyOCR期望的目录结构
        easyocr_home = Path.home() / ".EasyOCR"
        easyocr_home.mkdir(exist_ok=True)
        
        model_dir = easyocr_home / "model"
        model_dir.mkdir(exist_ok=True)
        
        # 复制模型文件到EasyOCR期望的位置
        source_dir = MODEL_DIR / "easyocr"
        if source_dir.exists():
            import shutil
            for model_file in source_dir.glob("*.pth"):
                target_file = model_dir / model_file.name
                if not target_file.exists():
                    shutil.copy2(model_file, target_file)
                    print(f"📋 复制 {model_file.name} 到 EasyOCR 目录")
        
        print("✅ EasyOCR 路径设置完成")
        return True
        
    except Exception as e:
        print(f"❌ EasyOCR 路径设置失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 模型下载工具启动")
    print(f"📁 模型目录: {MODEL_DIR.absolute()}")
    
    start_time = time.time()
    
    # 下载EasyOCR模型
    easyocr_success = download_easyocr_models()
    
    # 设置EasyOCR路径
    if easyocr_success:
        setup_easyocr_path()
    
    # 下载翻译模型
    translation_success = download_translation_model()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print("📊 下载总结")
    print("=" * 50)
    print(f"⏱️  总耗时: {duration:.1f} 秒")
    print(f"📦 EasyOCR模型: {'✅ 成功' if easyocr_success else '❌ 失败'}")
    print(f"🔤 翻译模型: {'✅ 成功' if translation_success else '❌ 失败'}")
    
    if easyocr_success and translation_success:
        print("\n🎉 所有模型下载完成！现在可以运行主程序了。")
        print("💡 运行命令: python start.py")
    else:
        print("\n⚠️  部分模型下载失败，请检查网络连接后重试。")
    
    return easyocr_success and translation_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
