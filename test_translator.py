#!/usr/bin/env python3
"""
测试翻译模型加载
"""

import os
import sys

def test_translator():
    """测试翻译器加载"""
    print("🔍 测试翻译模型加载...")
    
    try:
        # 设置离线模式
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_DATASETS_OFFLINE'] = '1'
        
        from transformers import MarianMTModel, MarianTokenizer
        
        # 模型路径
        MODEL_DIR = os.path.join(os.getcwd(), "models")
        model_path = os.path.join(MODEL_DIR, "models--Helsinki-NLP--opus-mt-zh-en")
        snapshot_path = os.path.join(model_path, "snapshots", "cf109095479db38d6df799875e34039d4938aaa6")
        
        print(f"📁 模型路径: {snapshot_path}")
        
        # 检查文件
        required_files = ["config.json", "pytorch_model.bin", "vocab.json", "tokenizer_config.json", "source.spm", "target.spm"]
        for file in required_files:
            file_path = os.path.join(snapshot_path, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file}: {size:,} bytes")
            else:
                print(f"  ❌ {file}: 不存在")
                return False
        
        print("\n🔄 尝试加载tokenizer...")
        try:
            tokenizer = MarianTokenizer.from_pretrained(snapshot_path, local_files_only=True)
            print("✅ Tokenizer加载成功")
        except Exception as e:
            print(f"❌ Tokenizer加载失败: {e}")
            print(f"错误类型: {type(e).__name__}")
            
            # 尝试不同的加载方式
            print("\n🔄 尝试使用模型名称加载...")
            try:
                tokenizer = MarianTokenizer.from_pretrained("Helsinki-NLP/opus-mt-zh-en", cache_dir=MODEL_DIR, local_files_only=True)
                print("✅ 使用模型名称加载成功")
            except Exception as e2:
                print(f"❌ 使用模型名称也失败: {e2}")
                return False
        
        print("\n🔄 尝试加载模型...")
        try:
            model = MarianMTModel.from_pretrained(snapshot_path, local_files_only=True)
            print("✅ Model加载成功")
        except Exception as e:
            print(f"❌ Model加载失败: {e}")
            return False
        
        print("\n🧪 测试翻译功能...")
        try:
            test_text = "你好世界"
            inputs = tokenizer(test_text, return_tensors="pt", padding=True)
            print(f"✅ 文本编码成功: {test_text}")
            
            with model.eval():
                outputs = model.generate(**inputs)
            
            translated = tokenizer.decode(outputs[0], skip_special_tokens=True)
            print(f"✅ 翻译成功: {test_text} -> {translated}")
            
        except Exception as e:
            print(f"❌ 翻译测试失败: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_approach():
    """测试替代方案"""
    print("\n🔄 测试替代翻译方案...")
    
    try:
        # 尝试使用不同的模型或方法
        from transformers import pipeline
        
        MODEL_DIR = os.path.join(os.getcwd(), "models")
        
        # 尝试使用pipeline
        print("🔄 尝试使用pipeline...")
        translator = pipeline(
            "translation", 
            model="Helsinki-NLP/opus-mt-zh-en",
            cache_dir=MODEL_DIR,
            local_files_only=True
        )
        
        result = translator("你好世界")
        print(f"✅ Pipeline翻译成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Pipeline方案失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 翻译模型诊断工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        print("❌ PyTorch未安装")
        return
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError:
        print("❌ Transformers未安装")
        return
    
    print()
    
    # 测试主要方案
    success = test_translator()
    
    if not success:
        # 测试替代方案
        success = test_alternative_approach()
    
    if success:
        print("\n🎉 翻译模型测试成功！")
    else:
        print("\n❌ 所有翻译方案都失败了")
        print("\n💡 建议:")
        print("1. 检查transformers库版本是否兼容")
        print("2. 尝试重新下载翻译模型")
        print("3. 使用简单翻译字典作为备选方案")

if __name__ == "__main__":
    main()
