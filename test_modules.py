#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试各个模块的功能
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def test_imports():
    """测试所有必要的库是否能正常导入"""
    print("测试库导入...")
    
    try:
        import easyocr
        print("✓ EasyOCR 导入成功")
    except ImportError as e:
        print(f"✗ EasyOCR 导入失败: {e}")
        return False
    
    try:
        from transformers import MarianMTModel, MarianTokenizer
        print("✓ Transformers 导入成功")
    except ImportError as e:
        print(f"✗ Transformers 导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✓ Pillow 导入成功")
    except ImportError as e:
        print(f"✗ Pillow 导入失败: {e}")
        return False
    
    try:
        import tkinter as tk
        print("✓ Tkinter 导入成功")
    except ImportError as e:
        print(f"✗ Tkinter 导入失败: {e}")
        return False
    
    return True

def test_ocr_init():
    """测试OCR模型初始化"""
    print("\n测试OCR模型初始化...")
    
    try:
        import easyocr
        reader = easyocr.Reader(['ch_sim', 'en'])
        print("✓ EasyOCR 模型初始化成功")
        return reader
    except Exception as e:
        print(f"✗ EasyOCR 模型初始化失败: {e}")
        return None

def test_translation_init():
    """测试翻译模型初始化"""
    print("\n测试翻译模型初始化...")
    
    try:
        from transformers import MarianMTModel, MarianTokenizer
        model_name = "Helsinki-NLP/opus-mt-zh-en"
        tokenizer = MarianTokenizer.from_pretrained(model_name)
        model = MarianMTModel.from_pretrained(model_name)
        print("✓ 翻译模型初始化成功")
        return tokenizer, model
    except Exception as e:
        print(f"✗ 翻译模型初始化失败: {e}")
        return None, None

def test_translation(tokenizer, model):
    """测试翻译功能"""
    print("\n测试翻译功能...")
    
    if tokenizer is None or model is None:
        print("✗ 翻译模型未初始化，跳过测试")
        return
    
    test_texts = ["你好", "世界", "图片", "文字识别"]
    
    for text in test_texts:
        try:
            inputs = tokenizer(text, return_tensors="pt", padding=True)
            translated = model.generate(**inputs)
            translated_text = tokenizer.decode(translated[0], skip_special_tokens=True)
            print(f"✓ '{text}' -> '{translated_text}'")
        except Exception as e:
            print(f"✗ 翻译 '{text}' 失败: {e}")

def create_test_image():
    """创建一个包含中文文字的测试图片"""
    print("\n创建测试图片...")
    
    try:
        # 创建一个白色背景的图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("simsun.ttc", 24)  # Windows系统字体
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
        
        # 添加中文文字
        texts = ["你好世界", "图片处理", "文字识别"]
        y_positions = [50, 100, 150]
        
        for text, y in zip(texts, y_positions):
            draw.text((50, y), text, fill='black', font=font)
        
        # 保存测试图片
        test_image_path = "test_image.png"
        img.save(test_image_path)
        print(f"✓ 测试图片已创建: {test_image_path}")
        return test_image_path
        
    except Exception as e:
        print(f"✗ 创建测试图片失败: {e}")
        return None

def test_ocr_on_image(reader, image_path):
    """测试OCR识别功能"""
    print(f"\n测试OCR识别功能...")
    
    if reader is None or image_path is None:
        print("✗ OCR模型或测试图片未准备好，跳过测试")
        return
    
    try:
        # 读取图片
        img = Image.open(image_path)
        img_array = np.array(img)
        
        # OCR识别
        results = reader.readtext(img_array)
        
        if results:
            print(f"✓ OCR识别成功，检测到 {len(results)} 个文字区域:")
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"  {i+1}. 文字: '{text}', 置信度: {confidence:.2f}")
        else:
            print("✗ OCR未检测到任何文字")
            
    except Exception as e:
        print(f"✗ OCR识别失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("图片文字翻译工具 - 模块测试")
    print("=" * 50)
    
    # 测试库导入
    if not test_imports():
        print("\n❌ 基础库导入测试失败，请检查依赖安装")
        return
    
    # 测试OCR初始化
    reader = test_ocr_init()
    
    # 测试翻译模型初始化
    tokenizer, model = test_translation_init()
    
    # 测试翻译功能
    test_translation(tokenizer, model)
    
    # 创建测试图片
    test_image_path = create_test_image()
    
    # 测试OCR识别
    test_ocr_on_image(reader, test_image_path)
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("如果所有测试都通过，您可以运行 'python start.py' 启动GUI应用")
    print("=" * 50)

if __name__ == "__main__":
    main()
