import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import os
import threading
import numpy as np

# 简单的中英文词典用于离线翻译
TRANSLATION_DICT = {
    '你好': 'Hello',
    '世界': 'World',
    '图片': 'Image',
    '文字': 'Text',
    '识别': 'Recognition',
    '翻译': 'Translation',
    '处理': 'Process',
    '保存': 'Save',
    '选择': 'Select',
    '开始': 'Start',
    '完成': 'Complete',
    '成功': 'Success',
    '失败': 'Failed',
    '错误': 'Error',
    '警告': 'Warning',
    '信息': 'Information',
    '文件': 'File',
    '照片': 'Photo',
    '相片': 'Picture',
    '中文': 'Chinese',
    '英文': 'English',
    '语言': 'Language',
    '工具': 'Tool',
    '软件': 'Software',
    '程序': 'Program',
    '应用': 'Application',
    '界面': 'Interface',
    '按钮': 'Button',
    '菜单': 'Menu',
    '窗口': 'Window',
    '对话框': 'Dialog',
    '设置': 'Settings',
    '配置': 'Configuration',
    '帮助': 'Help',
    '关于': 'About',
    '退出': 'Exit',
    '打开': 'Open',
    '关闭': 'Close',
    '新建': 'New',
    '编辑': 'Edit',
    '删除': 'Delete',
    '复制': 'Copy',
    '粘贴': 'Paste',
    '剪切': 'Cut',
    '撤销': 'Undo',
    '重做': 'Redo',
    '查找': 'Find',
    '替换': 'Replace',
    '全选': 'Select All',
    '清空': 'Clear',
    '刷新': 'Refresh',
    '更新': 'Update',
    '下载': 'Download',
    '上传': 'Upload',
    '导入': 'Import',
    '导出': 'Export',
    '打印': 'Print',
    '预览': 'Preview',
    '放大': 'Zoom In',
    '缩小': 'Zoom Out',
    '适应': 'Fit',
    '原始': 'Original',
    '大小': 'Size',
    '位置': 'Position',
    '颜色': 'Color',
    '字体': 'Font',
    '样式': 'Style',
    '格式': 'Format',
    '质量': 'Quality',
    '分辨率': 'Resolution',
    '像素': 'Pixel',
    '宽度': 'Width',
    '高度': 'Height',
    '长度': 'Length',
    '时间': 'Time',
    '日期': 'Date',
    '年': 'Year',
    '月': 'Month',
    '日': 'Day',
    '小时': 'Hour',
    '分钟': 'Minute',
    '秒': 'Second',
    '数字': 'Number',
    '字母': 'Letter',
    '符号': 'Symbol',
    '标点': 'Punctuation',
    '空格': 'Space',
    '换行': 'Line Break',
    '段落': 'Paragraph',
    '标题': 'Title',
    '内容': 'Content',
    '正文': 'Body',
    '页面': 'Page',
    '章节': 'Chapter',
    '部分': 'Section',
    '列表': 'List',
    '表格': 'Table',
    '图表': 'Chart',
    '图形': 'Graphics',
    '线条': 'Line',
    '圆形': 'Circle',
    '方形': 'Square',
    '矩形': 'Rectangle',
    '三角形': 'Triangle',
    '多边形': 'Polygon',
    '曲线': 'Curve',
    '直线': 'Straight Line',
    '角度': 'Angle',
    '距离': 'Distance',
    '面积': 'Area',
    '体积': 'Volume',
    '重量': 'Weight',
    '速度': 'Speed',
    '温度': 'Temperature',
    '压力': 'Pressure',
    '电压': 'Voltage',
    '电流': 'Current',
    '功率': 'Power',
    '能量': 'Energy',
    '频率': 'Frequency',
    '声音': 'Sound',
    '音乐': 'Music',
    '视频': 'Video',
    '音频': 'Audio',
    '媒体': 'Media',
    '网络': 'Network',
    '互联网': 'Internet',
    '网站': 'Website',
    '网页': 'Webpage',
    '链接': 'Link',
    '地址': 'Address',
    '邮件': 'Email',
    '消息': 'Message',
    '通知': 'Notification',
    '提醒': 'Reminder',
    '备忘': 'Memo',
    '笔记': 'Note',
    '记录': 'Record',
    '历史': 'History',
    '日志': 'Log',
    '报告': 'Report',
    '统计': 'Statistics',
    '分析': 'Analysis',
    '计算': 'Calculate',
    '结果': 'Result',
    '输入': 'Input',
    '输出': 'Output',
    '数据': 'Data',
    '信息': 'Information',
    '知识': 'Knowledge',
    '学习': 'Learning',
    '教育': 'Education',
    '培训': 'Training',
    '课程': 'Course',
    '教程': 'Tutorial',
    '指南': 'Guide',
    '手册': 'Manual',
    '文档': 'Document',
    '说明': 'Instruction',
    '描述': 'Description',
    '定义': 'Definition',
    '解释': 'Explanation',
    '示例': 'Example',
    '样本': 'Sample',
    '模板': 'Template',
    '格式': 'Format',
    '标准': 'Standard',
    '规则': 'Rule',
    '条件': 'Condition',
    '要求': 'Requirement',
    '限制': 'Limitation',
    '范围': 'Range',
    '级别': 'Level',
    '等级': 'Grade',
    '分类': 'Category',
    '类型': 'Type',
    '种类': 'Kind',
    '方式': 'Method',
    '方法': 'Approach',
    '技术': 'Technology',
    '技能': 'Skill',
    '能力': 'Ability',
    '功能': 'Function',
    '特性': 'Feature',
    '属性': 'Property',
    '参数': 'Parameter',
    '变量': 'Variable',
    '常量': 'Constant',
    '值': 'Value',
    '数值': 'Numeric Value',
    '字符': 'Character',
    '字符串': 'String',
    '文本': 'Text',
    '内容': 'Content',
    '主题': 'Topic',
    '话题': 'Subject',
    '问题': 'Problem',
    '答案': 'Answer',
    '解决': 'Solution',
    '方案': 'Plan',
    '策略': 'Strategy',
    '目标': 'Goal',
    '目的': 'Purpose',
    '意图': 'Intention',
    '原因': 'Reason',
    '结果': 'Result',
    '效果': 'Effect',
    '影响': 'Impact',
    '改变': 'Change',
    '更新': 'Update',
    '升级': 'Upgrade',
    '改进': 'Improvement',
    '优化': 'Optimization',
    '增强': 'Enhancement',
    '扩展': 'Extension',
    '添加': 'Add',
    '插入': 'Insert',
    '移除': 'Remove',
    '删除': 'Delete',
    '修改': 'Modify',
    '编辑': 'Edit',
    '调整': 'Adjust',
    '设置': 'Set',
    '配置': 'Configure',
    '安装': 'Install',
    '卸载': 'Uninstall',
    '运行': 'Run',
    '执行': 'Execute',
    '启动': 'Start',
    '停止': 'Stop',
    '暂停': 'Pause',
    '继续': 'Continue',
    '重启': 'Restart',
    '重置': 'Reset',
    '恢复': 'Restore',
    '备份': 'Backup',
    '同步': 'Sync',
    '连接': 'Connect',
    '断开': 'Disconnect',
    '登录': 'Login',
    '注销': 'Logout',
    '注册': 'Register',
    '验证': 'Verify',
    '确认': 'Confirm',
    '取消': 'Cancel',
    '接受': 'Accept',
    '拒绝': 'Reject',
    '同意': 'Agree',
    '不同意': 'Disagree',
    '是': 'Yes',
    '否': 'No',
    '真': 'True',
    '假': 'False',
    '正确': 'Correct',
    '错误': 'Wrong',
    '有效': 'Valid',
    '无效': 'Invalid',
    '可用': 'Available',
    '不可用': 'Unavailable',
    '启用': 'Enable',
    '禁用': 'Disable',
    '显示': 'Show',
    '隐藏': 'Hide',
    '可见': 'Visible',
    '不可见': 'Invisible',
    '公开': 'Public',
    '私有': 'Private',
    '安全': 'Security',
    '保护': 'Protection',
    '权限': 'Permission',
    '访问': 'Access',
    '控制': 'Control',
    '管理': 'Management',
    '监控': 'Monitor',
    '检查': 'Check',
    '测试': 'Test',
    '调试': 'Debug',
    '修复': 'Fix',
    '维护': 'Maintenance',
    '支持': 'Support',
    '服务': 'Service',
    '客户': 'Customer',
    '用户': 'User',
    '账户': 'Account',
    '配置文件': 'Profile',
    '设置': 'Settings',
    '偏好': 'Preferences',
    '选项': 'Options',
    '菜单': 'Menu',
    '工具栏': 'Toolbar',
    '状态栏': 'Status Bar',
    '进度条': 'Progress Bar',
    '滚动条': 'Scroll Bar',
    '标签页': 'Tab',
    '面板': 'Panel',
    '框架': 'Frame',
    '容器': 'Container',
    '组件': 'Component',
    '控件': 'Control',
    '元素': 'Element',
    '对象': 'Object',
    '实例': 'Instance',
    '类': 'Class',
    '方法': 'Method',
    '函数': 'Function',
    '过程': 'Procedure',
    '算法': 'Algorithm',
    '逻辑': 'Logic',
    '流程': 'Process',
    '步骤': 'Step',
    '阶段': 'Stage',
    '状态': 'Status',
    '模式': 'Mode',
    '版本': 'Version',
    '发布': 'Release',
    '更新': 'Update',
    '补丁': 'Patch',
    '修复': 'Fix',
    '错误': 'Bug',
    '问题': 'Issue',
    '故障': 'Fault',
    '异常': 'Exception',
    '警告': 'Warning',
    '提示': 'Tip',
    '建议': 'Suggestion',
    '推荐': 'Recommendation'
}

class SimpleImageTextTranslator:
    def __init__(self, root):
        self.root = root
        self.root.title("图片文字翻译工具 (离线版)")
        self.root.geometry("1200x800")
        
        # 图片相关变量
        self.original_image = None
        self.processed_image = None
        self.original_image_path = None
        
        # 创建GUI界面
        self.create_widgets()
        
        self.status_label.config(text="离线版本已就绪，请选择图片")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮
        self.select_button = ttk.Button(button_frame, text="选择图片", command=self.select_image)
        self.select_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.process_image)
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_button = ttk.Button(button_frame, text="保存图片", command=self.save_image, state="disabled")
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress = ttk.Progressbar(button_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 图片显示框架
        image_frame = ttk.Frame(main_frame)
        image_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        image_frame.columnconfigure(0, weight=1)
        image_frame.columnconfigure(1, weight=1)
        image_frame.rowconfigure(0, weight=1)
        
        # 原始图片显示
        original_frame = ttk.LabelFrame(image_frame, text="原始图片", padding="5")
        original_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        original_frame.columnconfigure(0, weight=1)
        original_frame.rowconfigure(0, weight=1)
        
        self.original_canvas = tk.Canvas(original_frame, bg="white")
        self.original_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 处理后图片显示
        processed_frame = ttk.LabelFrame(image_frame, text="处理后图片", padding="5")
        processed_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        processed_frame.columnconfigure(0, weight=1)
        processed_frame.rowconfigure(0, weight=1)
        
        self.processed_canvas = tk.Canvas(processed_frame, bg="white")
        self.processed_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="正在初始化...")
        self.status_label.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 说明标签
        info_label = ttk.Label(main_frame, text="注意：这是离线版本，使用简单的词典翻译。如需更准确的翻译，请确保网络连接正常后使用完整版本。",
                              foreground="blue", font=("Arial", 9))
        info_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

    def select_image(self):
        """选择图片文件"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.gif"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )

        if file_path:
            try:
                self.original_image_path = file_path
                self.original_image = Image.open(file_path)
                self.display_image(self.original_image, self.original_canvas)
                self.status_label.config(text=f"已选择图片: {os.path.basename(file_path)}")
                self.process_button.config(state="normal")

            except Exception as e:
                messagebox.showerror("错误", f"无法打开图片文件: {str(e)}")

    def display_image(self, image, canvas):
        """在画布上显示图片"""
        if image is None:
            return

        # 获取画布尺寸
        canvas.update()
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # 如果画布还没有正确初始化，使用默认尺寸
            canvas_width = 400
            canvas_height = 300

        # 计算缩放比例
        img_width, img_height = image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # 不放大图片

        # 调整图片尺寸
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为Tkinter可显示的格式
        photo = ImageTk.PhotoImage(resized_image)

        # 清除画布并显示图片
        canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        canvas.create_image(x, y, anchor=tk.NW, image=photo)

        # 保存引用以防止垃圾回收
        canvas.image = photo

    def simple_translate(self, text):
        """使用多种策略进行翻译"""
        # 移除空白字符
        text = text.strip()

        # 如果文本为空，返回空字符串
        if not text:
            return ""

        # 检查是否包含中文字符
        if not any('\u4e00' <= char <= '\u9fff' for char in text):
            return text  # 如果不包含中文，直接返回原文

        # 策略1：尝试完全匹配
        if text in TRANSLATION_DICT:
            return TRANSLATION_DICT[text]

        # 策略2：尝试拼音转换（简单的音译）
        pinyin_result = self.chinese_to_pinyin(text)
        if pinyin_result != text:
            return pinyin_result

        # 策略3：尝试部分匹配和组合翻译
        translated_parts = []
        i = 0
        while i < len(text):
            found = False
            # 尝试从最长的匹配开始
            for length in range(min(10, len(text) - i), 0, -1):
                substring = text[i:i+length]
                if substring in TRANSLATION_DICT:
                    translated_parts.append(TRANSLATION_DICT[substring])
                    i += length
                    found = True
                    break

            if not found:
                # 策略4：单字符音译
                char = text[i]
                pinyin_char = self.chinese_to_pinyin(char)
                if pinyin_char != char:
                    translated_parts.append(pinyin_char)
                else:
                    # 最后保留原字符
                    translated_parts.append(char)
                i += 1

        return ' '.join(translated_parts)

    def chinese_to_pinyin(self, text):
        """简单的中文转拼音映射"""
        # 常用汉字拼音映射表（部分）
        pinyin_dict = {
            '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu',
            '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi',
            '零': 'ling', '百': 'bai', '千': 'qian', '万': 'wan',
            '年': 'nian', '月': 'yue', '日': 'ri', '时': 'shi', '分': 'fen',
            '秒': 'miao', '上': 'shang', '下': 'xia', '左': 'zuo', '右': 'you',
            '前': 'qian', '后': 'hou', '中': 'zhong', '内': 'nei', '外': 'wai',
            '东': 'dong', '南': 'nan', '西': 'xi', '北': 'bei',
            '大': 'da', '小': 'xiao', '长': 'chang', '短': 'duan',
            '高': 'gao', '低': 'di', '新': 'xin', '旧': 'jiu',
            '好': 'hao', '坏': 'huai', '快': 'kuai', '慢': 'man',
            '多': 'duo', '少': 'shao', '远': 'yuan', '近': 'jin',
            '红': 'hong', '绿': 'lv', '蓝': 'lan', '黄': 'huang',
            '黑': 'hei', '白': 'bai', '灰': 'hui', '紫': 'zi',
            '人': 'ren', '男': 'nan', '女': 'nv', '老': 'lao', '少': 'shao',
            '爸': 'ba', '妈': 'ma', '儿': 'er', '女': 'nv', '子': 'zi',
            '家': 'jia', '房': 'fang', '门': 'men', '窗': 'chuang',
            '桌': 'zhuo', '椅': 'yi', '床': 'chuang', '灯': 'deng',
            '车': 'che', '船': 'chuan', '飞': 'fei', '机': 'ji',
            '路': 'lu', '桥': 'qiao', '山': 'shan', '水': 'shui',
            '树': 'shu', '花': 'hua', '草': 'cao', '鸟': 'niao',
            '鱼': 'yu', '猫': 'mao', '狗': 'gou', '马': 'ma',
            '吃': 'chi', '喝': 'he', '睡': 'shui', '走': 'zou',
            '跑': 'pao', '看': 'kan', '听': 'ting', '说': 'shuo',
            '读': 'du', '写': 'xie', '学': 'xue', '教': 'jiao',
            '买': 'mai', '卖': 'mai', '给': 'gei', '拿': 'na',
            '来': 'lai', '去': 'qu', '回': 'hui', '到': 'dao',
            '在': 'zai', '有': 'you', '没': 'mei', '是': 'shi',
            '不': 'bu', '很': 'hen', '太': 'tai', '非': 'fei',
            '常': 'chang', '也': 'ye', '都': 'dou', '还': 'hai',
            '就': 'jiu', '只': 'zhi', '能': 'neng', '会': 'hui',
            '要': 'yao', '想': 'xiang', '知': 'zhi', '道': 'dao',
            '明': 'ming', '天': 'tian', '今': 'jin', '昨': 'zuo',
            '早': 'zao', '晚': 'wan', '午': 'wu', '夜': 'ye',
            '春': 'chun', '夏': 'xia', '秋': 'qiu', '冬': 'dong',
            '热': 're', '冷': 'leng', '暖': 'nuan', '凉': 'liang',
            '晴': 'qing', '雨': 'yu', '雪': 'xue', '风': 'feng',
            '云': 'yun', '雷': 'lei', '电': 'dian', '光': 'guang',
            '火': 'huo', '土': 'tu', '金': 'jin', '木': 'mu',
            '米': 'mi', '面': 'mian', '菜': 'cai', '肉': 'rou',
            '鸡': 'ji', '鸭': 'ya', '鹅': 'e', '猪': 'zhu',
            '牛': 'niu', '羊': 'yang', '虎': 'hu', '兔': 'tu',
            '龙': 'long', '蛇': 'she', '马': 'ma', '羊': 'yang',
            '猴': 'hou', '鸡': 'ji', '狗': 'gou', '猪': 'zhu',
            '书': 'shu', '笔': 'bi', '纸': 'zhi', '本': 'ben',
            '包': 'bao', '衣': 'yi', '服': 'fu', '鞋': 'xie',
            '帽': 'mao', '手': 'shou', '脚': 'jiao', '头': 'tou',
            '眼': 'yan', '耳': 'er', '鼻': 'bi', '嘴': 'zui',
            '心': 'xin', '身': 'shen', '体': 'ti', '病': 'bing',
            '医': 'yi', '院': 'yuan', '药': 'yao', '针': 'zhen',
            '钱': 'qian', '元': 'yuan', '角': 'jiao', '分': 'fen',
            '块': 'kuai', '毛': 'mao', '贵': 'gui', '便': 'pian',
            '宜': 'yi', '店': 'dian', '市': 'shi', '场': 'chang',
            '银': 'yin', '行': 'xing', '邮': 'you', '局': 'ju',
            '电': 'dian', '话': 'hua', '网': 'wang', '络': 'luo',
            '电': 'dian', '脑': 'nao', '手': 'shou', '机': 'ji',
            '电': 'dian', '视': 'shi', '收': 'shou', '音': 'yin',
            '音': 'yin', '乐': 'le', '歌': 'ge', '舞': 'wu',
            '画': 'hua', '照': 'zhao', '片': 'pian', '相': 'xiang',
            '机': 'ji', '摄': 'she', '像': 'xiang', '录': 'lu',
            '像': 'xiang', '拍': 'pai', '摄': 'she', '制': 'zhi',
            '作': 'zuo', '创': 'chuang', '造': 'zao', '设': 'she',
            '计': 'ji', '建': 'jian', '筑': 'zhu', '房': 'fang',
            '屋': 'wu', '楼': 'lou', '层': 'ceng', '梯': 'ti',
            '电': 'dian', '梯': 'ti', '门': 'men', '锁': 'suo',
            '钥': 'yao', '匙': 'shi', '窗': 'chuang', '帘': 'lian',
            '墙': 'qiang', '地': 'di', '板': 'ban', '天': 'tian',
            '花': 'hua', '板': 'ban', '屋': 'wu', '顶': 'ding'
        }

        # 尝试整体翻译
        if text in pinyin_dict:
            return pinyin_dict[text]

        # 逐字符翻译
        result = []
        for char in text:
            if char in pinyin_dict:
                result.append(pinyin_dict[char])
            else:
                result.append(char)  # 保留无法翻译的字符

        # 如果有翻译结果，返回拼音；否则返回原文
        translated = ' '.join(result)
        if translated != text:
            return translated
        else:
            return text

    def get_font_for_text(self, text, max_width, max_height):
        """根据文本和区域大小选择合适的字体"""
        try:
            # 尝试使用系统字体
            font_size = min(max_height - 4, 20)  # 留一些边距
            font_size = max(font_size, 8)  # 最小字体大小

            # 尝试不同的字体
            font_names = ["arial.ttf", "Arial", "DejaVu Sans", "Liberation Sans"]

            for font_name in font_names:
                try:
                    font = ImageFont.truetype(font_name, font_size)
                    # 检查文本宽度是否合适
                    bbox = font.getbbox(text)
                    text_width = bbox[2] - bbox[0]

                    # 如果文本太宽，减小字体
                    while text_width > max_width - 4 and font_size > 8:
                        font_size -= 1
                        font = ImageFont.truetype(font_name, font_size)
                        bbox = font.getbbox(text)
                        text_width = bbox[2] - bbox[0]

                    return font
                except:
                    continue

            # 如果所有字体都失败，使用默认字体
            return ImageFont.load_default()

        except Exception as e:
            print(f"字体加载错误: {str(e)}")
            return ImageFont.load_default()

    def simulate_ocr(self, image):
        """模拟OCR功能 - 创建一些示例文字区域"""
        # 这是一个简化的演示版本
        # 在实际应用中，这里应该使用真正的OCR

        # 创建一些示例文字区域
        mock_results = [
            # (边界框, 文字, 置信度)
            ([(50, 50), (150, 50), (150, 80), (50, 80)], "你好", 0.9),
            ([(50, 100), (120, 100), (120, 130), (50, 130)], "世界", 0.8),
            ([(50, 150), (140, 150), (140, 180), (50, 180)], "图片", 0.85),
        ]

        return mock_results

    def process_image(self):
        """处理图片：模拟OCR识别、翻译、替换文字"""
        if self.original_image is None:
            messagebox.showwarning("警告", "请先选择图片")
            return

        # 在后台线程中处理图片
        def process_thread():
            try:
                self.progress.start()
                self.process_button.config(state="disabled")
                self.status_label.config(text="正在模拟识别图片中的文字...")
                self.root.update()

                # 模拟OCR识别
                results = self.simulate_ocr(self.original_image)

                if not results:
                    self.status_label.config(text="未检测到文字")
                    messagebox.showinfo("信息", "图片中未检测到文字")
                    return

                self.status_label.config(text="正在翻译文字...")
                self.root.update()

                # 创建处理后的图片副本
                processed_img = self.original_image.copy()
                draw = ImageDraw.Draw(processed_img)

                # 处理每个检测到的文字区域
                for (bbox, text, confidence) in results:
                    if confidence < 0.5:  # 跳过置信度太低的结果
                        continue

                    # 获取边界框坐标
                    top_left = tuple(map(int, bbox[0]))
                    bottom_right = tuple(map(int, bbox[2]))

                    # 计算文字区域尺寸
                    width = bottom_right[0] - top_left[0]
                    height = bottom_right[1] - top_left[1]

                    # 翻译文字
                    translated_text = self.simple_translate(text)

                    # 用白色矩形覆盖原文字
                    draw.rectangle([top_left, bottom_right], fill="white", outline="white")

                    # 选择合适的字体
                    font = self.get_font_for_text(translated_text, width, height)

                    # 计算文字位置（居中）
                    bbox_font = font.getbbox(translated_text)
                    text_width = bbox_font[2] - bbox_font[0]
                    text_height = bbox_font[3] - bbox_font[1]

                    text_x = top_left[0] + (width - text_width) // 2
                    text_y = top_left[1] + (height - text_height) // 2

                    # 绘制英文文字
                    draw.text((text_x, text_y), translated_text, fill="black", font=font)

                # 保存处理后的图片
                self.processed_image = processed_img

                # 在主线程中更新UI
                self.root.after(0, self.update_ui_after_processing)

            except Exception as e:
                error_msg = f"处理图片时出错: {str(e)}"
                self.root.after(0, lambda: self.handle_processing_error(error_msg))

            finally:
                self.root.after(0, self.finish_processing)

        thread = threading.Thread(target=process_thread)
        thread.daemon = True
        thread.start()

    def update_ui_after_processing(self):
        """处理完成后更新UI"""
        self.display_image(self.processed_image, self.processed_canvas)
        self.save_button.config(state="normal")
        self.status_label.config(text="图片处理完成")

    def handle_processing_error(self, error_msg):
        """处理错误"""
        self.status_label.config(text="处理失败")
        messagebox.showerror("错误", error_msg)

    def finish_processing(self):
        """完成处理的清理工作"""
        self.progress.stop()
        self.process_button.config(state="normal")

    def save_image(self):
        """保存处理后的图片"""
        if self.processed_image is None:
            messagebox.showwarning("警告", "没有可保存的图片")
            return

        try:
            # 生成保存文件名
            if self.original_image_path:
                base_name = os.path.splitext(os.path.basename(self.original_image_path))[0]
                extension = os.path.splitext(self.original_image_path)[1]
                save_filename = f"{base_name}_translated{extension}"
            else:
                save_filename = "translated_image.png"

            # 保存到ok文件夹
            ok_folder = "ok"
            if not os.path.exists(ok_folder):
                os.makedirs(ok_folder)

            save_path = os.path.join(ok_folder, save_filename)

            # 如果文件已存在，添加数字后缀
            counter = 1
            original_save_path = save_path
            while os.path.exists(save_path):
                name_part = os.path.splitext(original_save_path)[0]
                ext_part = os.path.splitext(original_save_path)[1]
                save_path = f"{name_part}_{counter}{ext_part}"
                counter += 1

            # 保存图片
            self.processed_image.save(save_path)

            self.status_label.config(text=f"图片已保存到: {save_path}")
            messagebox.showinfo("成功", f"图片已保存到: {save_path}")

        except Exception as e:
            error_msg = f"保存图片失败: {str(e)}"
            self.status_label.config(text="保存失败")
            messagebox.showerror("错误", error_msg)


def main():
    """主程序入口"""
    root = tk.Tk()
    app = SimpleImageTextTranslator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
