#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试离线翻译功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from start_offline import SimpleImageTextTranslator
import tkinter as tk

def test_translation_strategies():
    """测试不同的翻译策略"""
    print("=" * 60)
    print("离线翻译功能测试")
    print("=" * 60)
    
    # 创建一个临时的翻译器实例来测试翻译功能
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    translator = SimpleImageTextTranslator(root)
    
    # 测试用例
    test_cases = [
        # 词典中存在的词汇
        "你好",
        "世界", 
        "图片",
        "文字",
        "翻译",
        
        # 词典中不存在但有拼音的词汇
        "电脑",
        "手机",
        "音乐",
        "照片",
        "房屋",
        
        # 组合词汇
        "你好世界",
        "图片处理",
        "文字识别",
        "电脑软件",
        
        # 包含数字的词汇
        "一二三",
        "十年",
        "三个人",
        
        # 复杂词汇
        "人工智能",
        "机器学习",
        "深度学习",
        "神经网络",
        
        # 日常用语
        "早上好",
        "晚安",
        "谢谢",
        "再见",
        
        # 颜色词汇
        "红色",
        "蓝天",
        "绿树",
        "黄花",
        
        # 动物词汇
        "小猫",
        "大狗",
        "白马",
        "黑鸟",
        
        # 完全未知的词汇（测试拼音转换）
        "璀璨",
        "瑰丽",
        "磅礴",
        "恢弘"
    ]
    
    print("测试不同翻译策略的效果：\n")
    
    for i, text in enumerate(test_cases, 1):
        try:
            translated = translator.simple_translate(text)
            strategy = get_translation_strategy(translator, text, translated)
            
            print(f"{i:2d}. 原文: '{text}' -> 译文: '{translated}' [{strategy}]")
            
        except Exception as e:
            print(f"{i:2d}. 原文: '{text}' -> 翻译失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("翻译策略说明：")
    print("1. 词典匹配 - 直接从预定义词典中查找")
    print("2. 拼音转换 - 将中文转换为拼音")
    print("3. 组合翻译 - 拆分后分别翻译再组合")
    print("4. 混合策略 - 结合多种方法")
    print("=" * 60)
    
    root.destroy()

def get_translation_strategy(translator, original, translated):
    """判断使用了哪种翻译策略"""
    # 检查是否是词典直接匹配
    from start_offline import TRANSLATION_DICT
    if original in TRANSLATION_DICT:
        return "词典匹配"
    
    # 检查是否是拼音转换
    pinyin_result = translator.chinese_to_pinyin(original)
    if translated == pinyin_result and pinyin_result != original:
        return "拼音转换"
    
    # 检查是否包含空格（组合翻译的特征）
    if ' ' in translated and len(original) > 1:
        return "组合翻译"
    
    # 检查是否保留了原文
    if translated == original:
        return "保留原文"
    
    return "混合策略"

def test_pinyin_conversion():
    """专门测试拼音转换功能"""
    print("\n" + "=" * 60)
    print("拼音转换功能测试")
    print("=" * 60)
    
    root = tk.Tk()
    root.withdraw()
    translator = SimpleImageTextTranslator(root)
    
    # 测试单字符拼音转换
    test_chars = ["人", "天", "地", "山", "水", "火", "木", "金", "土", "日", "月", "星"]
    
    print("单字符拼音转换：")
    for char in test_chars:
        pinyin = translator.chinese_to_pinyin(char)
        print(f"'{char}' -> '{pinyin}'")
    
    print("\n多字符拼音转换：")
    test_words = ["天空", "大地", "山水", "日月", "星空", "火山"]
    for word in test_words:
        pinyin = translator.chinese_to_pinyin(word)
        print(f"'{word}' -> '{pinyin}'")
    
    root.destroy()

def demonstrate_improvement():
    """演示改进效果"""
    print("\n" + "=" * 60)
    print("离线翻译改进效果对比")
    print("=" * 60)
    
    print("改进前的限制：")
    print("- 只能翻译预定义词典中的约200个词汇")
    print("- 遇到未知词汇时直接保留原文")
    print("- 无法处理组合词汇")
    
    print("\n改进后的能力：")
    print("- 词典词汇：直接精确翻译")
    print("- 常用汉字：转换为拼音")
    print("- 组合词汇：拆分后分别处理")
    print("- 混合文本：多策略结合处理")
    
    print("\n覆盖范围对比：")
    print("- 改进前：~200个预定义词汇")
    print("- 改进后：~200个词汇 + ~200个拼音字符 + 无限组合")
    
    print("\n注意事项：")
    print("- 拼音翻译仅为音译，不是意译")
    print("- 复杂词汇可能需要人工校对")
    print("- 专业术语建议使用完整版本")

def main():
    """主测试函数"""
    print("开始测试离线翻译功能...")
    
    try:
        # 测试翻译策略
        test_translation_strategies()
        
        # 测试拼音转换
        test_pinyin_conversion()
        
        # 演示改进效果
        demonstrate_improvement()
        
        print("\n🎉 测试完成！")
        print("\n建议：")
        print("1. 对于常用词汇，离线版本效果良好")
        print("2. 对于专业术语，建议使用完整版本")
        print("3. 可以根据需要扩展词典和拼音表")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
