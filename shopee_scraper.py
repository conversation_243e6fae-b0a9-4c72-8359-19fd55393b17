#!/usr/bin/env python3
"""
Shopee商品标题爬虫
抓取指定店铺的所有商品标题并保存为txt文件
"""

import requests
import time
import json
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
import os

class ShopeeScraper:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        self.session.headers.update(self.headers)
        
    def get_shop_info(self, shop_url):
        """获取店铺信息"""
        print(f"🔍 正在分析店铺URL: {shop_url}")
        
        # 从URL中提取店铺名称
        if 'shopee.sg/' in shop_url:
            shop_name = shop_url.split('shopee.sg/')[-1].split('#')[0].split('?')[0]
            print(f"📍 店铺名称: {shop_name}")
            return shop_name
        else:
            raise ValueError("无效的Shopee店铺URL")
    
    def get_shop_products_api(self, shop_name, limit=50, offset=0):
        """通过API获取店铺商品"""
        # Shopee API端点
        api_url = f"https://shopee.sg/api/v4/shop/search_items"
        
        params = {
            'by': 'relevancy',
            'limit': limit,
            'match_id': shop_name,
            'newest': offset,
            'order': 'desc',
            'page_type': 'shop',
            'scenario': 'PAGE_SHOP_SEARCH',
            'version': 2
        }
        
        try:
            print(f"🌐 请求API: {api_url}")
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ API请求异常: {e}")
            return None
    
    def extract_products_from_html(self, shop_url):
        """从HTML页面提取商品信息"""
        print(f"🌐 正在访问页面: {shop_url}")
        
        try:
            response = self.session.get(shop_url, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ 页面访问失败: {response.status_code}")
                return []
            
            html_content = response.text
            print(f"✅ 页面加载成功，内容长度: {len(html_content)}")
            
            # 查找JSON数据
            products = []
            
            # 方法1: 查找__NEXT_DATA__
            next_data_pattern = r'<script id="__NEXT_DATA__" type="application/json">(.*?)</script>'
            next_data_match = re.search(next_data_pattern, html_content, re.DOTALL)
            
            if next_data_match:
                try:
                    json_data = json.loads(next_data_match.group(1))
                    print("✅ 找到__NEXT_DATA__")
                    
                    # 递归搜索商品数据
                    products.extend(self.extract_products_from_json(json_data))
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
            
            # 方法2: 查找其他可能的JSON结构
            if not products:
                # 查找商品标题的其他模式
                title_patterns = [
                    r'"name":"([^"]+)"',
                    r'"title":"([^"]+)"',
                    r'data-testid=".*?title.*?"[^>]*>([^<]+)<',
                    r'class=".*?product.*?title.*?"[^>]*>([^<]+)<'
                ]
                
                for pattern in title_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    if matches:
                        print(f"✅ 通过模式匹配找到 {len(matches)} 个标题")
                        for match in matches:
                            # 清理标题
                            title = self.clean_title(match)
                            if title and len(title) > 3:  # 过滤太短的标题
                                products.append({'name': title})
                        break
            
            return products
            
        except Exception as e:
            print(f"❌ 页面解析异常: {e}")
            return []
    
    def extract_products_from_json(self, data, products=None):
        """递归从JSON数据中提取商品信息"""
        if products is None:
            products = []
        
        if isinstance(data, dict):
            # 查找商品相关的键
            for key, value in data.items():
                if key in ['items', 'products', 'data', 'item_list']:
                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                # 查找商品名称
                                name = item.get('name') or item.get('title') or item.get('item_name')
                                if name:
                                    products.append({'name': self.clean_title(name)})
                
                # 递归搜索
                if isinstance(value, (dict, list)):
                    self.extract_products_from_json(value, products)
        
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    self.extract_products_from_json(item, products)
        
        return products
    
    def clean_title(self, title):
        """清理商品标题"""
        if not title:
            return ""
        
        # 解码Unicode转义字符
        try:
            title = title.encode().decode('unicode_escape')
        except:
            pass
        
        # 移除多余的空白字符
        title = re.sub(r'\s+', ' ', title).strip()
        
        # 移除特殊字符
        title = re.sub(r'[^\w\s\-\(\)\[\]\/\&\+\.\,\!\?\:\;]', '', title)
        
        return title
    
    def scrape_shop(self, shop_url):
        """爬取店铺所有商品"""
        print("🚀 开始爬取Shopee店铺商品")
        print("=" * 50)
        
        try:
            # 获取店铺信息
            shop_name = self.get_shop_info(shop_url)
            
            all_products = []
            
            # 方法1: 尝试API
            print("\n📡 尝试通过API获取商品...")
            api_products = self.get_shop_products_api(shop_name)
            if api_products and 'items' in api_products:
                for item in api_products['items']:
                    name = item.get('name') or item.get('title')
                    if name:
                        all_products.append({'name': self.clean_title(name)})
                print(f"✅ API获取到 {len(all_products)} 个商品")
            
            # 方法2: 如果API失败，尝试HTML解析
            if not all_products:
                print("\n🌐 尝试通过HTML解析获取商品...")
                html_products = self.extract_products_from_html(shop_url)
                all_products.extend(html_products)
                print(f"✅ HTML解析获取到 {len(all_products)} 个商品")
            
            # 去重
            unique_products = []
            seen_titles = set()
            for product in all_products:
                title = product['name']
                if title and title not in seen_titles:
                    unique_products.append(product)
                    seen_titles.add(title)
            
            print(f"\n📊 总计获取到 {len(unique_products)} 个唯一商品")
            return unique_products, shop_name
            
        except Exception as e:
            print(f"❌ 爬取失败: {e}")
            import traceback
            traceback.print_exc()
            return [], ""
    
    def save_to_file(self, products, shop_name):
        """保存商品标题到文件"""
        if not products:
            print("❌ 没有商品数据可保存")
            return
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"shopee_{shop_name}_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Shopee店铺商品标题\n")
                f.write(f"店铺: {shop_name}\n")
                f.write(f"抓取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"商品数量: {len(products)}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, product in enumerate(products, 1):
                    f.write(f"{i:3d}. {product['name']}\n")
            
            print(f"✅ 商品标题已保存到: {filename}")
            print(f"📁 文件路径: {os.path.abspath(filename)}")
            
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")

def main():
    """主函数"""
    shop_url = "https://shopee.sg/sinokigo.sg#product_list"
    
    scraper = ShopeeScraper()
    products, shop_name = scraper.scrape_shop(shop_url)
    
    if products:
        scraper.save_to_file(products, shop_name)
        
        # 显示前几个商品作为预览
        print(f"\n📋 商品标题预览 (前10个):")
        for i, product in enumerate(products[:10], 1):
            print(f"  {i:2d}. {product['name']}")
        
        if len(products) > 10:
            print(f"  ... 还有 {len(products) - 10} 个商品")
    else:
        print("❌ 未能获取到商品信息")
        print("\n💡 可能的原因:")
        print("1. 网站有反爬虫保护")
        print("2. 页面结构已改变")
        print("3. 需要登录才能访问")
        print("4. 网络连接问题")

if __name__ == "__main__":
    main()
