#!/usr/bin/env python3
"""
EasyOCR GPU测试版本
专门测试EasyOCR在GPU上的运行
"""

import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import numpy as np
from pathlib import Path

# EasyOCR模型目录（用户目录）
EASYOCR_MODEL_DIR = os.path.join(os.path.expanduser("~"), ".EasyOCR", "model")
os.makedirs(EASYOCR_MODEL_DIR, exist_ok=True)

# 设置环境变量
os.environ['EASYOCR_MODULE_PATH'] = os.path.join(os.path.expanduser("~"), ".EasyOCR")

class EasyOCRGPUTestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("EasyOCR GPU测试工具")
        self.root.geometry("900x700")
        
        # 初始化变量
        self.original_image = None
        self.processed_image = None
        self.ocr_reader = None
        self.translator = None
        self.tokenizer = None
        self.torch = None
        self.device = None
        
        # 创建界面
        self.create_widgets()
        
        # 在后台初始化模型
        self.init_thread = threading.Thread(target=self.initialize_easyocr)
        self.init_thread.daemon = True
        self.init_thread.start()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 EasyOCR GPU测试工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 选择图片按钮
        self.select_btn = ttk.Button(button_frame, text="📁 选择图片", command=self.select_image, state='disabled')
        self.select_btn.grid(row=0, column=0, padx=(0, 10))
        
        # OCR识别按钮
        self.ocr_btn = ttk.Button(button_frame, text="🔍 GPU识别", command=self.recognize_text, state='disabled')
        self.ocr_btn.grid(row=0, column=1, padx=(0, 10))

        # 翻译按钮
        self.translate_btn = ttk.Button(button_frame, text="🔄 翻译图片", command=self.translate_image, state='disabled')
        self.translate_btn.grid(row=0, column=2, padx=(0, 10))

        # 保存按钮
        self.save_btn = ttk.Button(button_frame, text="💾 保存结果", command=self.save_image, state='disabled')
        self.save_btn.grid(row=0, column=3, padx=(0, 10))

        # 性能测试按钮
        self.benchmark_btn = ttk.Button(button_frame, text="⚡ 性能测试", command=self.run_benchmark, state='disabled')
        self.benchmark_btn.grid(row=0, column=4)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="正在初始化EasyOCR GPU模式...")
        self.status_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        self.progress.start()
        
        # 图片和结果框架
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 图片显示框架
        image_frame = ttk.LabelFrame(content_frame, text="📷 原图", padding="10")
        image_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))

        self.image_label = ttk.Label(image_frame, text="请选择图片")
        self.image_label.grid(row=0, column=0)

        # 翻译结果图片框架
        result_image_frame = ttk.LabelFrame(content_frame, text="🔤 翻译结果", padding="10")
        result_image_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(2, 2))

        self.result_image_label = ttk.Label(result_image_frame, text="等待翻译")
        self.result_image_label.grid(row=0, column=0)

        # 文字结果显示框架
        result_frame = ttk.LabelFrame(content_frame, text="📝 识别详情", padding="10")
        result_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(2, 0))
        
        # 结果文本框
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, width=50, height=20)
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 系统信息框架
        info_frame = ttk.LabelFrame(main_frame, text="🖥️ 系统信息", padding="10")
        info_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.info_label = ttk.Label(info_frame, text="正在检测GPU信息...")
        self.info_label.grid(row=0, column=0)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.columnconfigure(2, weight=1)
        content_frame.rowconfigure(0, weight=1)
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)
        result_image_frame.columnconfigure(0, weight=1)
        result_image_frame.rowconfigure(0, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def update_status(self, message):
        """更新状态"""
        print(message)
        if hasattr(self, 'status_label'):
            self.root.after(0, lambda: self.status_label.config(text=message))
    
    def update_info(self, message):
        """更新系统信息"""
        if hasattr(self, 'info_label'):
            self.root.after(0, lambda: self.info_label.config(text=message))
    
    def check_local_models(self):
        """检查本地模型文件"""
        self.update_status("检查本地模型文件...")
        required_models = ["craft_mlt_25k.pth", "zh_sim_g2.pth", "english_g2.pth"]
        
        for model in required_models:
            model_path = os.path.join(EASYOCR_MODEL_DIR, model)
            if os.path.exists(model_path):
                size_mb = os.path.getsize(model_path) / 1024 / 1024
                print(f"  ✅ {model}: {size_mb:.1f} MB")
            else:
                print(f"  ❌ {model}: 不存在")
                raise FileNotFoundError(f"缺少模型文件: {model}")
        
        print("✅ 所有EasyOCR模型文件检查完成")
    
    def initialize_easyocr(self):
        """初始化EasyOCR"""
        try:
            # 检查GPU
            import torch
            self.torch = torch
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            self.device = device
            
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                self.update_status(f"GPU: {gpu_name}")
                self.update_info(f"🖥️ GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                
                # 清理GPU内存
                torch.cuda.empty_cache()
                print(f"✅ GPU可用: {gpu_name}")
            else:
                self.update_info("🖥️ GPU: 不可用，使用CPU")
                print("⚠️ GPU不可用，将使用CPU")
            
            # 检查本地模型
            self.check_local_models()
            
            # 初始化EasyOCR（GPU优化）
            self.update_status("正在初始化EasyOCR (GPU模式)...")
            import easyocr
            
            # 强制使用GPU并指定模型目录
            gpu_enabled = torch.cuda.is_available()
            self.update_status(f"EasyOCR GPU模式: {'启用' if gpu_enabled else '禁用'}")
            
            self.ocr_reader = easyocr.Reader(
                ['ch_sim', 'en'], 
                gpu=gpu_enabled,
                model_storage_directory=EASYOCR_MODEL_DIR,
                download_enabled=False,  # 禁用自动下载
                verbose=True
            )
            
            print("✅ EasyOCR初始化完成")

            # 初始化翻译器（GPU优化）
            self.update_status("正在初始化翻译器 (GPU模式)...")
            try:
                from transformers import MarianMTModel, MarianTokenizer
                import os

                # 设置模型目录
                MODEL_DIR = os.path.join(os.getcwd(), "models")
                model_path = os.path.join(MODEL_DIR, "models--Helsinki-NLP--opus-mt-zh-en")

                if os.path.exists(model_path):
                    print(f"✅ 找到本地翻译模型: {model_path}")

                    # 设置环境变量强制离线模式
                    os.environ['TRANSFORMERS_OFFLINE'] = '1'
                    os.environ['HF_DATASETS_OFFLINE'] = '1'

                    # 查找实际的模型文件路径
                    snapshots_dir = os.path.join(model_path, "snapshots")
                    if os.path.exists(snapshots_dir):
                        # 找到包含模型文件的snapshot目录
                        for snapshot in os.listdir(snapshots_dir):
                            snapshot_path = os.path.join(snapshots_dir, snapshot)
                            if os.path.isdir(snapshot_path):
                                config_file = os.path.join(snapshot_path, "config.json")
                                model_file = os.path.join(snapshot_path, "pytorch_model.bin")
                                if os.path.exists(config_file) and os.path.exists(model_file):
                                    print(f"✅ 使用模型快照: {snapshot}")

                                    # 直接从本地路径加载
                                    self.tokenizer = MarianTokenizer.from_pretrained(
                                        snapshot_path,
                                        local_files_only=True
                                    )
                                    self.translator = MarianMTModel.from_pretrained(
                                        snapshot_path,
                                        local_files_only=True
                                    )
                                    break
                        else:
                            raise FileNotFoundError("未找到有效的模型快照")
                    else:
                        raise FileNotFoundError("未找到snapshots目录")

                    # 将翻译模型移到GPU
                    if torch.cuda.is_available():
                        self.update_status("将翻译模型移至GPU...")
                        self.translator = self.translator.to('cuda')
                        print("✅ 翻译模型已移至GPU")

                    print("✅ 翻译器初始化完成")

                else:
                    print("⚠️ 本地翻译模型不存在")
                    self.translator = None
                    self.tokenizer = None

            except Exception as e:
                print(f"⚠️ 翻译器初始化失败: {e}")
                print("将使用简单翻译功能")
                self.translator = None
                self.tokenizer = None

            # 初始化完成
            self.root.after(0, self.on_models_ready)
            
        except Exception as e:
            error_msg = f"EasyOCR初始化失败: {str(e)}"
            print(error_msg)
            print(f"详细错误: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
            self.root.after(0, lambda: self.update_status(error_msg))
    
    def on_models_ready(self):
        """模型准备就绪"""
        self.progress.stop()
        self.progress.grid_remove()
        self.update_status("✅ EasyOCR已就绪，请选择图片")
        self.select_btn.config(state='normal')
        self.benchmark_btn.config(state='normal')
        
        # 更新系统信息
        if self.torch and self.torch.cuda.is_available():
            gpu_name = self.torch.cuda.get_device_name(0)
            memory_allocated = self.torch.cuda.memory_allocated(0) / 1024**3
            memory_total = self.torch.cuda.get_device_properties(0).total_memory / 1024**3
            info_text = f"🖥️ GPU: {gpu_name} | 内存: {memory_allocated:.1f}GB / {memory_total:.1f}GB"
        else:
            info_text = "🖥️ 设备: CPU模式"
        
        self.update_info(info_text)
    
    def select_image(self):
        """选择图片"""
        file_path = filedialog.askopenfilename(
            title="选择图片",
            filetypes=[("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff")]
        )
        
        if file_path:
            try:
                # 加载图片
                self.original_image = Image.open(file_path)
                
                # 调整显示大小
                display_image = self.resize_for_display(self.original_image)
                photo = ImageTk.PhotoImage(display_image)
                
                self.image_label.config(image=photo, text="")
                self.image_label.image = photo
                
                # 启用OCR和翻译按钮
                self.ocr_btn.config(state='normal')
                if self.translator and self.tokenizer:
                    self.translate_btn.config(state='normal')
                self.update_status("图片已加载，可以开始识别或翻译")

                # 清空结果
                self.result_text.delete(1.0, tk.END)
                self.result_image_label.config(image="", text="等待翻译")
                
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片: {str(e)}")
    
    def resize_for_display(self, image, max_size=350):
        """调整图片显示大小"""
        width, height = image.size
        if width > max_size or height > max_size:
            ratio = min(max_size/width, max_size/height)
            new_size = (int(width*ratio), int(height*ratio))
            return image.resize(new_size, Image.Resampling.LANCZOS)
        return image
    
    def recognize_text(self):
        """识别文字"""
        if not self.original_image or not self.ocr_reader:
            return
        
        # 在新线程中处理
        thread = threading.Thread(target=self.process_ocr)
        thread.daemon = True
        thread.start()
    
    def process_ocr(self):
        """处理OCR识别"""
        try:
            import time
            start_time = time.time()
            
            self.update_status("🔍 正在识别文字 (GPU加速)...")
            
            # OCR识别
            image_array = np.array(self.original_image)
            results = self.ocr_reader.readtext(image_array)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 显示结果
            self.root.after(0, lambda: self.show_ocr_results(results, processing_time))
            
        except Exception as e:
            error_msg = f"OCR识别失败: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.root.after(0, lambda: self.update_status(error_msg))
    
    def show_ocr_results(self, results, processing_time):
        """显示OCR结果"""
        self.result_text.delete(1.0, tk.END)
        
        # 显示性能信息
        self.result_text.insert(tk.END, f"⚡ 处理时间: {processing_time:.2f} 秒\n")
        self.result_text.insert(tk.END, f"🖥️ 设备: {'GPU' if self.device == 'cuda' else 'CPU'}\n\n")
        
        if not results:
            self.result_text.insert(tk.END, "未检测到文字\n")
            self.update_status("❌ 未检测到文字")
            return
        
        self.result_text.insert(tk.END, f"检测到 {len(results)} 个文字区域:\n\n")
        
        for i, (bbox, text, confidence) in enumerate(results, 1):
            self.result_text.insert(tk.END, f"区域 {i}:\n")
            self.result_text.insert(tk.END, f"  文字: {text}\n")
            self.result_text.insert(tk.END, f"  置信度: {confidence:.3f}\n")
            self.result_text.insert(tk.END, f"  位置: {bbox}\n\n")
        
        self.update_status(f"✅ 识别完成，找到 {len(results)} 个文字区域 ({processing_time:.2f}s)")

    def translate_image(self):
        """翻译图片"""
        if not self.original_image or not self.ocr_reader:
            return

        if not self.translator or not self.tokenizer:
            messagebox.showwarning("警告", "翻译模型未加载，请先确保翻译模型正常初始化")
            return

        # 在新线程中处理
        thread = threading.Thread(target=self.process_translation)
        thread.daemon = True
        thread.start()

    def process_translation(self):
        """处理翻译过程"""
        try:
            import time
            start_time = time.time()

            self.update_status("🔍 正在识别文字 (GPU加速)...")

            # OCR识别
            image_array = np.array(self.original_image)
            results = self.ocr_reader.readtext(image_array)

            if not results:
                self.root.after(0, lambda: self.update_status("❌ 未检测到文字"))
                return

            self.update_status(f"🔄 正在翻译 {len(results)} 个文字区域 (GPU加速)...")

            # 创建结果图片
            processed_image = self.original_image.copy()
            draw = ImageDraw.Draw(processed_image)

            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()

            # 处理每个文字区域
            translation_results = []
            for bbox, text, confidence in results:
                if confidence > 0.5:  # 只处理置信度高的文字
                    # 翻译文字
                    translated = self.translate_text(text)
                    translation_results.append((text, translated, confidence))

                    # 计算文字位置
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)

                    # 绘制白色背景
                    draw.rectangle([x_min-2, y_min-2, x_max+2, y_max+2], fill='white', outline='red')

                    # 绘制翻译文字
                    draw.text((x_min, y_min), translated, fill='black', font=font)

            self.processed_image = processed_image
            end_time = time.time()
            processing_time = end_time - start_time

            # 显示结果
            self.root.after(0, lambda: self.show_translation_result(translation_results, processing_time))

        except Exception as e:
            error_msg = f"翻译失败: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.root.after(0, lambda: self.update_status(error_msg))

    def translate_text(self, text):
        """翻译文字"""
        try:
            if not self.translator or not self.tokenizer:
                return text

            # 检查是否包含中文字符
            if not any('\u4e00' <= char <= '\u9fff' for char in text):
                return text  # 如果不包含中文，直接返回原文

            # 编码输入
            inputs = self.tokenizer(text, return_tensors="pt", padding=True)

            # 移动到GPU
            if self.torch.cuda.is_available() and hasattr(self.translator, 'device'):
                inputs = {k: v.to('cuda') for k, v in inputs.items()}

            # 生成翻译
            with self.torch.no_grad():
                outputs = self.translator.generate(**inputs)

            # 解码结果
            translated = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            return translated

        except Exception as e:
            print(f"翻译错误: {e}")
            return text

    def show_translation_result(self, translation_results, processing_time):
        """显示翻译结果"""
        # 显示翻译后的图片
        if self.processed_image:
            display_image = self.resize_for_display(self.processed_image, max_size=300)
            photo = ImageTk.PhotoImage(display_image)

            self.result_image_label.config(image=photo, text="")
            self.result_image_label.image = photo

            self.save_btn.config(state='normal')

        # 显示翻译详情
        self.result_text.delete(1.0, tk.END)

        # 显示性能信息
        self.result_text.insert(tk.END, f"⚡ 处理时间: {processing_time:.2f} 秒\n")
        self.result_text.insert(tk.END, f"🖥️ 设备: {'GPU' if self.device == 'cuda' else 'CPU'}\n\n")

        if not translation_results:
            self.result_text.insert(tk.END, "未检测到中文文字\n")
            self.update_status("❌ 未检测到中文文字")
            return

        self.result_text.insert(tk.END, f"翻译了 {len(translation_results)} 个文字区域:\n\n")

        for i, (original, translated, confidence) in enumerate(translation_results, 1):
            self.result_text.insert(tk.END, f"区域 {i}:\n")
            self.result_text.insert(tk.END, f"  原文: {original}\n")
            self.result_text.insert(tk.END, f"  译文: {translated}\n")
            self.result_text.insert(tk.END, f"  置信度: {confidence:.3f}\n\n")

        self.update_status(f"✅ 翻译完成，处理 {len(translation_results)} 个区域 ({processing_time:.2f}s)")

    def save_image(self):
        """保存图片"""
        if not self.processed_image:
            return

        file_path = filedialog.asksaveasfilename(
            title="保存翻译结果",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("JPEG文件", "*.jpg"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.processed_image.save(file_path)
                messagebox.showinfo("成功", "翻译结果已保存！")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def run_benchmark(self):
        """运行性能测试"""
        if not self.ocr_reader:
            return
        
        # 在新线程中处理
        thread = threading.Thread(target=self.process_benchmark)
        thread.daemon = True
        thread.start()
    
    def process_benchmark(self):
        """处理性能测试"""
        try:
            import time
            
            self.update_status("⚡ 正在进行性能测试...")
            
            # 创建测试图片
            test_image = Image.new('RGB', (800, 600), color='white')
            draw = ImageDraw.Draw(test_image)
            
            # 添加测试文字
            try:
                font = ImageFont.truetype("arial.ttf", 48)
            except:
                font = ImageFont.load_default()
            
            test_texts = [
                "Hello World 你好世界",
                "Performance Test 性能测试", 
                "GPU Acceleration GPU加速",
                "EasyOCR Benchmark 基准测试"
            ]
            
            for i, text in enumerate(test_texts):
                draw.text((50, 100 + i * 80), text, fill='black', font=font)
            
            # 转换为numpy数组
            image_array = np.array(test_image)
            
            # 进行多次测试
            times = []
            for i in range(3):
                self.update_status(f"⚡ 性能测试 {i+1}/3...")
                start_time = time.time()
                results = self.ocr_reader.readtext(image_array)
                end_time = time.time()
                times.append(end_time - start_time)
            
            # 计算统计信息
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            # 显示结果
            self.root.after(0, lambda: self.show_benchmark_results(times, avg_time, min_time, max_time))
            
        except Exception as e:
            error_msg = f"性能测试失败: {str(e)}"
            print(error_msg)
            self.root.after(0, lambda: self.update_status(error_msg))
    
    def show_benchmark_results(self, times, avg_time, min_time, max_time):
        """显示性能测试结果"""
        self.result_text.delete(1.0, tk.END)
        
        self.result_text.insert(tk.END, "⚡ 性能测试结果\n")
        self.result_text.insert(tk.END, "=" * 30 + "\n\n")
        
        self.result_text.insert(tk.END, f"🖥️ 设备: {'GPU' if self.device == 'cuda' else 'CPU'}\n")
        if self.torch and self.torch.cuda.is_available():
            gpu_name = self.torch.cuda.get_device_name(0)
            self.result_text.insert(tk.END, f"🎮 GPU: {gpu_name}\n")
        
        self.result_text.insert(tk.END, f"\n📊 测试结果:\n")
        for i, time_taken in enumerate(times, 1):
            self.result_text.insert(tk.END, f"  测试 {i}: {time_taken:.3f} 秒\n")
        
        self.result_text.insert(tk.END, f"\n📈 统计信息:\n")
        self.result_text.insert(tk.END, f"  平均时间: {avg_time:.3f} 秒\n")
        self.result_text.insert(tk.END, f"  最快时间: {min_time:.3f} 秒\n")
        self.result_text.insert(tk.END, f"  最慢时间: {max_time:.3f} 秒\n")
        
        # 性能评估
        if avg_time < 1.0:
            performance = "🚀 优秀"
        elif avg_time < 2.0:
            performance = "✅ 良好"
        elif avg_time < 5.0:
            performance = "⚠️ 一般"
        else:
            performance = "🐌 较慢"
        
        self.result_text.insert(tk.END, f"\n🏆 性能评级: {performance}\n")
        
        self.update_status(f"✅ 性能测试完成，平均时间: {avg_time:.3f}s")

def main():
    """主函数"""
    print("🚀 启动EasyOCR GPU测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.cuda.get_device_name(0)}")
            print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        else:
            print("⚠️ CUDA不可用，将使用CPU")
    except ImportError:
        print("❌ 请先安装PyTorch")
        return
    
    try:
        import easyocr
        print("✅ EasyOCR: 已安装")
    except ImportError:
        print("❌ 请先安装EasyOCR")
        return
    
    # 启动GUI
    root = tk.Tk()
    app = EasyOCRGPUTestApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
