#!/usr/bin/env python3
"""
使用Selenium的Shopee商品标题爬虫
通过浏览器自动化来抓取商品信息
"""

import time
import json
import re
from datetime import datetime
import os

def install_selenium():
    """安装Selenium和webdriver"""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        return True
    except ImportError:
        print("❌ Selenium未安装，正在安装...")
        import subprocess
        try:
            subprocess.check_call(["pip", "install", "selenium", "webdriver-manager"])
            print("✅ Selenium安装成功")
            return True
        except Exception as e:
            print(f"❌ Selenium安装失败: {e}")
            return False

class ShopeeSeleniumScraper:
    def __init__(self):
        self.driver = None
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 如果需要无头模式，取消下面的注释
            # chrome_options.add_argument('--headless')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行脚本来隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome浏览器驱动设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def scrape_shopee_products(self, shop_url):
        """使用Selenium抓取Shopee商品"""
        print(f"🌐 正在访问: {shop_url}")
        
        try:
            # 访问页面
            self.driver.get(shop_url)
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 滚动页面以加载更多商品
            print("📜 滚动页面加载更多商品...")
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            scroll_count = 0
            max_scrolls = 10  # 最多滚动10次
            
            while scroll_count < max_scrolls:
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待新内容加载
                time.sleep(3)
                
                # 检查是否有新内容加载
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                
                last_height = new_height
                scroll_count += 1
                print(f"  📜 第 {scroll_count} 次滚动...")
            
            # 查找商品元素
            products = []
            
            # 尝试多种选择器
            selectors = [
                '[data-testid*="product"]',
                '.shopee-search-item-result__item',
                '.item-card-special',
                '.col-xs-2-4',
                '[class*="item"]',
                '[class*="product"]'
            ]
            
            for selector in selectors:
                try:
                    from selenium.webdriver.common.by import By
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        print(f"✅ 使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                        
                        for element in elements:
                            try:
                                # 尝试多种方式获取商品标题
                                title = None
                                
                                # 方法1: 查找标题元素
                                title_selectors = [
                                    '[data-testid*="title"]',
                                    '.shopee-item-card__title',
                                    '[class*="title"]',
                                    'a[title]',
                                    'img[alt]'
                                ]
                                
                                for title_selector in title_selectors:
                                    try:
                                        title_element = element.find_element(By.CSS_SELECTOR, title_selector)
                                        title = title_element.get_attribute('title') or title_element.get_attribute('alt') or title_element.text
                                        if title and len(title.strip()) > 3:
                                            break
                                    except:
                                        continue
                                
                                # 方法2: 如果没找到，尝试获取元素文本
                                if not title:
                                    title = element.text.strip()
                                
                                if title and len(title.strip()) > 3:
                                    # 清理标题
                                    title = self.clean_title(title)
                                    if title:
                                        products.append({'name': title})
                                        
                            except Exception as e:
                                continue
                        
                        if products:
                            break
                            
                except Exception as e:
                    continue
            
            # 如果还是没找到，尝试从页面源码中提取
            if not products:
                print("🔍 从页面源码中查找商品信息...")
                page_source = self.driver.page_source
                products = self.extract_from_page_source(page_source)
            
            return products
            
        except Exception as e:
            print(f"❌ 页面抓取失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def extract_from_page_source(self, page_source):
        """从页面源码中提取商品信息"""
        products = []
        
        try:
            # 查找JSON数据
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__NEXT_DATA__\s*=\s*({.*?});',
                r'"items":\s*(\[.*?\])',
                r'"products":\s*(\[.*?\])'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, page_source, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        extracted = self.extract_products_from_json(data)
                        products.extend(extracted)
                        if products:
                            print(f"✅ 从JSON数据中提取到 {len(extracted)} 个商品")
                            break
                    except:
                        continue
                if products:
                    break
            
            # 如果JSON提取失败，尝试正则表达式
            if not products:
                title_patterns = [
                    r'"name":"([^"]+)"',
                    r'"title":"([^"]+)"',
                    r'title="([^"]+)"',
                    r'alt="([^"]+)"'
                ]
                
                for pattern in title_patterns:
                    matches = re.findall(pattern, page_source)
                    if matches:
                        for match in matches:
                            title = self.clean_title(match)
                            if title and len(title) > 3:
                                products.append({'name': title})
                        
                        if products:
                            print(f"✅ 通过正则表达式找到 {len(products)} 个商品标题")
                            break
            
        except Exception as e:
            print(f"❌ 页面源码解析失败: {e}")
        
        return products
    
    def extract_products_from_json(self, data, products=None):
        """递归从JSON数据中提取商品信息"""
        if products is None:
            products = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                if key in ['items', 'products', 'data', 'item_list', 'searchItems']:
                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                name = item.get('name') or item.get('title') or item.get('item_name')
                                if name:
                                    products.append({'name': self.clean_title(name)})
                
                if isinstance(value, (dict, list)):
                    self.extract_products_from_json(value, products)
        
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    self.extract_products_from_json(item, products)
        
        return products
    
    def clean_title(self, title):
        """清理商品标题"""
        if not title:
            return ""
        
        # 解码Unicode转义字符
        try:
            title = title.encode().decode('unicode_escape')
        except:
            pass
        
        # 移除多余的空白字符
        title = re.sub(r'\s+', ' ', title).strip()
        
        # 过滤掉明显不是商品标题的内容
        if any(keyword in title.lower() for keyword in ['shopee', 'login', 'register', 'cart', 'search', 'menu']):
            return ""
        
        # 长度过滤
        if len(title) < 5 or len(title) > 200:
            return ""
        
        return title
    
    def save_to_file(self, products, shop_name):
        """保存商品标题到文件"""
        if not products:
            print("❌ 没有商品数据可保存")
            return
        
        # 去重
        unique_products = []
        seen_titles = set()
        for product in products:
            title = product['name']
            if title and title not in seen_titles:
                unique_products.append(product)
                seen_titles.add(title)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"shopee_{shop_name}_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Shopee店铺商品标题\n")
                f.write(f"店铺: {shop_name}\n")
                f.write(f"抓取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"商品数量: {len(unique_products)}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, product in enumerate(unique_products, 1):
                    f.write(f"{i:3d}. {product['name']}\n")
            
            print(f"✅ 商品标题已保存到: {filename}")
            print(f"📁 文件路径: {os.path.abspath(filename)}")
            return unique_products
            
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")
            return []
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    print("🚀 Shopee商品标题爬虫 (Selenium版本)")
    print("=" * 50)
    
    # 检查并安装依赖
    if not install_selenium():
        return
    
    shop_url = "https://shopee.sg/sinokigo.sg#product_list"
    shop_name = "sinokigo.sg"
    
    scraper = ShopeeSeleniumScraper()
    
    try:
        # 设置浏览器驱动
        if not scraper.setup_driver():
            return
        
        # 抓取商品
        products = scraper.scrape_shopee_products(shop_url)
        
        if products:
            # 保存到文件
            unique_products = scraper.save_to_file(products, shop_name)
            
            # 显示预览
            print(f"\n📋 商品标题预览 (前10个):")
            for i, product in enumerate(unique_products[:10], 1):
                print(f"  {i:2d}. {product['name']}")
            
            if len(unique_products) > 10:
                print(f"  ... 还有 {len(unique_products) - 10} 个商品")
        else:
            print("❌ 未能获取到商品信息")
            print("\n💡 建议:")
            print("1. 检查网络连接")
            print("2. 确认店铺URL是否正确")
            print("3. 可能需要手动验证或登录")
    
    finally:
        # 关闭浏览器
        scraper.close()

if __name__ == "__main__":
    main()
