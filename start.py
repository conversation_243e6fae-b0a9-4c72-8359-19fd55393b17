import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import easyocr
from transformers import MarianMTModel, MarianTokenizer
import os
import threading
import numpy as np

# 设置模型缓存目录为项目根目录
MODEL_DIR = os.path.join(os.getcwd(), "models")
os.makedirs(MODEL_DIR, exist_ok=True)

# 设置环境变量，让模型下载到项目目录
os.environ['EASYOCR_MODULE_PATH'] = MODEL_DIR
os.environ['TRANSFORMERS_CACHE'] = MODEL_DIR

class ImageTextTranslator:
    def __init__(self, root):
        self.root = root
        self.root.title("图片文字翻译工具")
        self.root.geometry("1200x800")

        # 初始化OCR和翻译模型
        self.ocr_reader = None
        self.translator_model = None
        self.translator_tokenizer = None

        # 图片相关变量
        self.original_image = None
        self.processed_image = None
        self.original_image_path = None

        # 创建GUI界面
        self.create_widgets()

        # 在后台初始化模型
        self.init_models_thread()

    def init_models_thread(self):
        """在后台线程中初始化模型"""
        def init_models():
            try:
                self.status_label.config(text="正在初始化OCR模型...")
                self.root.update()

                # 初始化EasyOCR
                self.ocr_reader = easyocr.Reader(['ch_sim', 'en'])

                self.status_label.config(text="正在初始化翻译模型...")
                self.root.update()

                # 初始化翻译模型
                model_name = "Helsinki-NLP/opus-mt-zh-en"
                self.translator_tokenizer = MarianTokenizer.from_pretrained(model_name)
                self.translator_model = MarianMTModel.from_pretrained(model_name)

                self.status_label.config(text="模型初始化完成，请选择图片")
                self.process_button.config(state="normal")

            except Exception as e:
                self.status_label.config(text=f"模型初始化失败: {str(e)}")
                messagebox.showerror("错误", f"模型初始化失败: {str(e)}")

        thread = threading.Thread(target=init_models)
        thread.daemon = True
        thread.start()

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 按钮
        self.select_button = ttk.Button(button_frame, text="选择图片", command=self.select_image)
        self.select_button.pack(side=tk.LEFT, padx=(0, 10))

        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.process_image, state="disabled")
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))

        self.save_button = ttk.Button(button_frame, text="保存图片", command=self.save_image, state="disabled")
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))

        # 进度条
        self.progress = ttk.Progressbar(button_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))

        # 图片显示框架
        image_frame = ttk.Frame(main_frame)
        image_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        image_frame.columnconfigure(0, weight=1)
        image_frame.columnconfigure(1, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # 原始图片显示
        original_frame = ttk.LabelFrame(image_frame, text="原始图片", padding="5")
        original_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        original_frame.columnconfigure(0, weight=1)
        original_frame.rowconfigure(0, weight=1)

        self.original_canvas = tk.Canvas(original_frame, bg="white")
        self.original_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 处理后图片显示
        processed_frame = ttk.LabelFrame(image_frame, text="处理后图片", padding="5")
        processed_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        processed_frame.columnconfigure(0, weight=1)
        processed_frame.rowconfigure(0, weight=1)

        self.processed_canvas = tk.Canvas(processed_frame, bg="white")
        self.processed_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 状态标签
        self.status_label = ttk.Label(main_frame, text="正在初始化模型，请稍候...")
        self.status_label.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def select_image(self):
        """选择图片文件"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.gif"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )

        if file_path:
            try:
                self.original_image_path = file_path
                self.original_image = Image.open(file_path)
                self.display_image(self.original_image, self.original_canvas)
                self.status_label.config(text=f"已选择图片: {os.path.basename(file_path)}")

                # 如果模型已初始化，启用处理按钮
                if self.ocr_reader is not None and self.translator_model is not None:
                    self.process_button.config(state="normal")

            except Exception as e:
                messagebox.showerror("错误", f"无法打开图片文件: {str(e)}")

    def display_image(self, image, canvas):
        """在画布上显示图片"""
        if image is None:
            return

        # 获取画布尺寸
        canvas.update()
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # 如果画布还没有正确初始化，使用默认尺寸
            canvas_width = 400
            canvas_height = 300

        # 计算缩放比例
        img_width, img_height = image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # 不放大图片

        # 调整图片尺寸
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为Tkinter可显示的格式
        photo = ImageTk.PhotoImage(resized_image)

        # 清除画布并显示图片
        canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        canvas.create_image(x, y, anchor=tk.NW, image=photo)

        # 保存引用以防止垃圾回收
        canvas.image = photo

    def translate_text(self, text):
        """翻译中文文本为英文"""
        try:
            # 检查是否包含中文字符
            if not any('\u4e00' <= char <= '\u9fff' for char in text):
                return text  # 如果不包含中文，直接返回原文

            # 使用翻译模型
            inputs = self.translator_tokenizer(text, return_tensors="pt", padding=True)
            translated = self.translator_model.generate(**inputs)
            translated_text = self.translator_tokenizer.decode(translated[0], skip_special_tokens=True)

            return translated_text.strip()

        except Exception as e:
            print(f"翻译错误: {str(e)}")
            return text  # 翻译失败时返回原文

    def get_font_for_text(self, text, max_width, max_height):
        """根据文本和区域大小选择合适的字体"""
        try:
            # 尝试使用系统字体
            font_size = min(max_height - 4, 20)  # 留一些边距
            font_size = max(font_size, 8)  # 最小字体大小

            # 尝试不同的字体
            font_names = ["arial.ttf", "Arial", "DejaVu Sans", "Liberation Sans"]

            for font_name in font_names:
                try:
                    font = ImageFont.truetype(font_name, font_size)
                    # 检查文本宽度是否合适
                    bbox = font.getbbox(text)
                    text_width = bbox[2] - bbox[0]

                    # 如果文本太宽，减小字体
                    while text_width > max_width - 4 and font_size > 8:
                        font_size -= 1
                        font = ImageFont.truetype(font_name, font_size)
                        bbox = font.getbbox(text)
                        text_width = bbox[2] - bbox[0]

                    return font
                except:
                    continue

            # 如果所有字体都失败，使用默认字体
            return ImageFont.load_default()

        except Exception as e:
            print(f"字体加载错误: {str(e)}")
            return ImageFont.load_default()

    def process_image(self):
        """处理图片：OCR识别、翻译、替换文字"""
        if self.original_image is None:
            messagebox.showwarning("警告", "请先选择图片")
            return

        if self.ocr_reader is None or self.translator_model is None:
            messagebox.showwarning("警告", "模型尚未初始化完成")
            return

        # 在后台线程中处理图片
        def process_thread():
            try:
                self.progress.start()
                self.process_button.config(state="disabled")
                self.status_label.config(text="正在识别图片中的文字...")
                self.root.update()

                # 将PIL图片转换为numpy数组供EasyOCR使用
                img_array = np.array(self.original_image)

                # OCR识别
                results = self.ocr_reader.readtext(img_array)

                if not results:
                    self.status_label.config(text="未检测到文字")
                    messagebox.showinfo("信息", "图片中未检测到文字")
                    return

                self.status_label.config(text="正在翻译文字...")
                self.root.update()

                # 创建处理后的图片副本
                processed_img = self.original_image.copy()
                draw = ImageDraw.Draw(processed_img)

                # 处理每个检测到的文字区域
                for (bbox, text, confidence) in results:
                    if confidence < 0.5:  # 跳过置信度太低的结果
                        continue

                    # 获取边界框坐标
                    top_left = tuple(map(int, bbox[0]))
                    bottom_right = tuple(map(int, bbox[2]))

                    # 计算文字区域尺寸
                    width = bottom_right[0] - top_left[0]
                    height = bottom_right[1] - top_left[1]

                    # 翻译文字
                    translated_text = self.translate_text(text)

                    # 用白色矩形覆盖原文字
                    draw.rectangle([top_left, bottom_right], fill="white", outline="white")

                    # 选择合适的字体
                    font = self.get_font_for_text(translated_text, width, height)

                    # 计算文字位置（居中）
                    bbox_font = font.getbbox(translated_text)
                    text_width = bbox_font[2] - bbox_font[0]
                    text_height = bbox_font[3] - bbox_font[1]

                    text_x = top_left[0] + (width - text_width) // 2
                    text_y = top_left[1] + (height - text_height) // 2

                    # 绘制英文文字
                    draw.text((text_x, text_y), translated_text, fill="black", font=font)

                # 保存处理后的图片
                self.processed_image = processed_img

                # 在主线程中更新UI
                self.root.after(0, self.update_ui_after_processing)

            except Exception as e:
                error_msg = f"处理图片时出错: {str(e)}"
                self.root.after(0, lambda: self.handle_processing_error(error_msg))

            finally:
                self.root.after(0, self.finish_processing)

        thread = threading.Thread(target=process_thread)
        thread.daemon = True
        thread.start()

    def update_ui_after_processing(self):
        """处理完成后更新UI"""
        self.display_image(self.processed_image, self.processed_canvas)
        self.save_button.config(state="normal")
        self.status_label.config(text="图片处理完成")

    def handle_processing_error(self, error_msg):
        """处理错误"""
        self.status_label.config(text="处理失败")
        messagebox.showerror("错误", error_msg)

    def finish_processing(self):
        """完成处理的清理工作"""
        self.progress.stop()
        self.process_button.config(state="normal")

    def save_image(self):
        """保存处理后的图片"""
        if self.processed_image is None:
            messagebox.showwarning("警告", "没有可保存的图片")
            return

        try:
            # 生成保存文件名
            if self.original_image_path:
                base_name = os.path.splitext(os.path.basename(self.original_image_path))[0]
                extension = os.path.splitext(self.original_image_path)[1]
                save_filename = f"{base_name}_translated{extension}"
            else:
                save_filename = "translated_image.png"

            # 保存到ok文件夹
            ok_folder = "ok"
            if not os.path.exists(ok_folder):
                os.makedirs(ok_folder)

            save_path = os.path.join(ok_folder, save_filename)

            # 如果文件已存在，添加数字后缀
            counter = 1
            original_save_path = save_path
            while os.path.exists(save_path):
                name_part = os.path.splitext(original_save_path)[0]
                ext_part = os.path.splitext(original_save_path)[1]
                save_path = f"{name_part}_{counter}{ext_part}"
                counter += 1

            # 保存图片
            self.processed_image.save(save_path)

            self.status_label.config(text=f"图片已保存到: {save_path}")
            messagebox.showinfo("成功", f"图片已保存到: {save_path}")

        except Exception as e:
            error_msg = f"保存图片失败: {str(e)}"
            self.status_label.config(text="保存失败")
            messagebox.showerror("错误", error_msg)


def main():
    """主程序入口"""
    root = tk.Tk()
    app = ImageTextTranslator(root)
    root.mainloop()


if __name__ == "__main__":
    main()