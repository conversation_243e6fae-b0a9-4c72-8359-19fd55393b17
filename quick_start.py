#!/usr/bin/env python3
"""
快速启动脚本
检查模型状态并智能启动程序
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def check_easyocr_models():
    """检查EasyOCR模型是否已下载"""
    easyocr_path = Path.home() / ".EasyOCR" / "model"
    
    required_models = [
        "craft_mlt_25k.pth",
        "chinese_g2.pth", 
        "english_g2.pth"
    ]
    
    if not easyocr_path.exists():
        return False
    
    existing_models = [f.name for f in easyocr_path.glob("*.pth")]
    
    for model in required_models:
        if model not in existing_models:
            return False
    
    return True

def check_transformers_models():
    """检查翻译模型是否已下载"""
    models_dir = Path("models")
    
    if not models_dir.exists():
        return False
    
    # 检查是否有Helsinki-NLP相关文件
    helsinki_files = list(models_dir.glob("**/Helsinki-NLP*"))
    
    return len(helsinki_files) > 0

def start_program():
    """启动主程序"""
    print("🚀 启动图片文字翻译工具...")
    
    # 检查模型状态
    easyocr_ready = check_easyocr_models()
    transformers_ready = check_transformers_models()
    
    print(f"📦 EasyOCR模型: {'✅ 已就绪' if easyocr_ready else '❌ 需要下载'}")
    print(f"🔤 翻译模型: {'✅ 已就绪' if transformers_ready else '❌ 需要下载'}")
    
    if easyocr_ready and transformers_ready:
        print("🎉 所有模型已就绪，启动完整版本...")
        subprocess.run([sys.executable, "start_mirror.py"])
    elif easyocr_ready:
        print("⚡ EasyOCR已就绪，启动并下载翻译模型...")
        subprocess.run([sys.executable, "start_mirror.py"])
    else:
        print("📥 需要下载模型，启动镜像版本...")
        print("💡 如果下载卡住，请按 Ctrl+C 重新启动")
        
        try:
            subprocess.run([sys.executable, "start_mirror.py"])
        except KeyboardInterrupt:
            print("\n🔄 检测到中断，重新启动...")
            time.sleep(1)
            start_program()

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 图片文字翻译工具 - 智能启动器")
    print("=" * 50)
    
    # 检查基本依赖
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA: {'可用' if torch.cuda.is_available() else '不可用'}")
        if torch.cuda.is_available():
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("❌ PyTorch未安装")
        return
    
    try:
        import easyocr
        print("✅ EasyOCR: 已安装")
    except ImportError:
        print("❌ EasyOCR未安装")
        return
    
    try:
        import transformers
        print("✅ Transformers: 已安装")
    except ImportError:
        print("❌ Transformers未安装")
        return
    
    print("\n" + "=" * 50)
    
    # 启动程序
    start_program()

if __name__ == "__main__":
    main()
