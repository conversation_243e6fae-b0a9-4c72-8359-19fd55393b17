#!/usr/bin/env python3
"""
高级Shopee商品标题爬虫
使用多种策略绕过反爬虫保护
"""

import requests
import time
import json
import re
from datetime import datetime
import os
import random
from urllib.parse import urljoin, urlparse

def install_beautifulsoup():
    """安装BeautifulSoup"""
    try:
        from bs4 import BeautifulSoup
        return True
    except ImportError:
        print("❌ BeautifulSoup未安装，正在安装...")
        import subprocess
        try:
            subprocess.check_call(["pip", "install", "beautifulsoup4", "lxml"])
            print("✅ BeautifulSoup安装成功")
            return True
        except Exception as e:
            print(f"❌ BeautifulSoup安装失败: {e}")
            return False

class AdvancedShopeeScraper:
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """设置会话参数"""
        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
        
        self.session.headers.update(headers)
        
        # 设置代理（如果需要）
        # self.session.proxies = {'http': 'http://proxy:port', 'https': 'https://proxy:port'}
    
    def get_page_with_retry(self, url, max_retries=3):
        """带重试的页面获取"""
        for attempt in range(max_retries):
            try:
                print(f"🌐 尝试访问页面 (第 {attempt + 1} 次): {url}")
                
                # 随机延迟
                time.sleep(random.uniform(2, 5))
                
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    print(f"✅ 页面访问成功，内容长度: {len(response.text)}")
                    return response.text
                elif response.status_code == 403:
                    print(f"❌ 访问被拒绝 (403)，可能被反爬虫检测")
                    # 更换User-Agent重试
                    self.setup_session()
                elif response.status_code == 429:
                    print(f"❌ 请求过于频繁 (429)，等待更长时间...")
                    time.sleep(random.uniform(10, 20))
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                time.sleep(random.uniform(5, 10))
        
        return None
    
    def extract_products_from_html(self, html_content):
        """从HTML中提取商品信息"""
        try:
            from bs4 import BeautifulSoup
        except ImportError:
            print("❌ BeautifulSoup未安装")
            return []
        
        products = []
        
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 方法1: 查找JSON数据
            script_tags = soup.find_all('script')
            for script in script_tags:
                if script.string:
                    # 查找包含商品数据的JSON
                    if 'items' in script.string or 'products' in script.string:
                        try:
                            # 尝试提取JSON数据
                            json_match = re.search(r'({.*"items".*})', script.string, re.DOTALL)
                            if not json_match:
                                json_match = re.search(r'({.*"products".*})', script.string, re.DOTALL)
                            
                            if json_match:
                                data = json.loads(json_match.group(1))
                                extracted = self.extract_products_from_json(data)
                                products.extend(extracted)
                                if products:
                                    print(f"✅ 从script标签中提取到 {len(extracted)} 个商品")
                                    break
                        except:
                            continue
            
            # 方法2: 查找商品链接和标题
            if not products:
                # 查找商品链接
                product_links = soup.find_all('a', href=re.compile(r'/.*-i\.\d+\.\d+'))
                
                for link in product_links:
                    title = None
                    
                    # 从链接的title属性获取
                    if link.get('title'):
                        title = link.get('title')
                    
                    # 从链接内的图片alt属性获取
                    elif link.find('img'):
                        img = link.find('img')
                        title = img.get('alt') or img.get('title')
                    
                    # 从链接内的文本获取
                    elif link.get_text(strip=True):
                        title = link.get_text(strip=True)
                    
                    if title:
                        title = self.clean_title(title)
                        if title and len(title) > 5:
                            products.append({'name': title})
                
                if products:
                    print(f"✅ 从HTML链接中提取到 {len(products)} 个商品")
            
            # 方法3: 查找特定的CSS选择器
            if not products:
                selectors = [
                    '[data-testid*="product"]',
                    '.shopee-search-item-result__item',
                    '.item-card-special',
                    '[class*="item-card"]',
                    '[class*="product-card"]'
                ]
                
                for selector in selectors:
                    elements = soup.select(selector)
                    if elements:
                        print(f"✅ 使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                        
                        for element in elements:
                            # 查找标题
                            title_element = (
                                element.find(attrs={'data-testid': re.compile(r'.*title.*')}) or
                                element.find('img') or
                                element.find('a')
                            )
                            
                            if title_element:
                                title = (
                                    title_element.get('title') or
                                    title_element.get('alt') or
                                    title_element.get_text(strip=True)
                                )
                                
                                if title:
                                    title = self.clean_title(title)
                                    if title and len(title) > 5:
                                        products.append({'name': title})
                        
                        if products:
                            break
            
        except Exception as e:
            print(f"❌ HTML解析失败: {e}")
        
        return products
    
    def extract_products_from_json(self, data, products=None):
        """递归从JSON数据中提取商品信息"""
        if products is None:
            products = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                if key in ['items', 'products', 'data', 'item_list', 'searchItems']:
                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                name = item.get('name') or item.get('title') or item.get('item_name')
                                if name:
                                    products.append({'name': self.clean_title(name)})
                
                if isinstance(value, (dict, list)):
                    self.extract_products_from_json(value, products)
        
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    self.extract_products_from_json(item, products)
        
        return products
    
    def clean_title(self, title):
        """清理商品标题"""
        if not title:
            return ""
        
        # 解码HTML实体
        try:
            import html
            title = html.unescape(title)
        except:
            pass
        
        # 移除多余的空白字符
        title = re.sub(r'\s+', ' ', title).strip()
        
        # 过滤掉明显不是商品标题的内容
        filter_keywords = [
            'shopee', 'login', 'register', 'cart', 'search', 'menu',
            'follow', 'chat', 'view', 'more', 'see all', 'show more'
        ]
        
        if any(keyword in title.lower() for keyword in filter_keywords):
            return ""
        
        # 长度过滤
        if len(title) < 5 or len(title) > 200:
            return ""
        
        return title
    
    def try_api_endpoints(self, shop_name):
        """尝试不同的API端点"""
        api_endpoints = [
            f"https://shopee.sg/api/v4/shop/search_items?by=relevancy&limit=50&match_id={shop_name}&newest=0&order=desc&page_type=shop&scenario=PAGE_SHOP_SEARCH&version=2",
            f"https://shopee.sg/api/v2/shop/{shop_name}/search_items?limit=50&offset=0&order=desc&sort_by=relevancy",
            f"https://shopee.sg/api/v4/search/search_items?by=relevancy&keyword=&limit=50&match_id={shop_name}&order=desc&page_type=shop"
        ]
        
        for api_url in api_endpoints:
            try:
                print(f"🔗 尝试API: {api_url}")
                response = self.session.get(api_url, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    if 'items' in data and data['items']:
                        products = []
                        for item in data['items']:
                            name = item.get('name') or item.get('title')
                            if name:
                                products.append({'name': self.clean_title(name)})
                        
                        if products:
                            print(f"✅ API成功获取到 {len(products)} 个商品")
                            return products
                
            except Exception as e:
                print(f"❌ API请求失败: {e}")
                continue
        
        return []
    
    def scrape_shop(self, shop_url):
        """爬取店铺商品"""
        print("🚀 开始爬取Shopee店铺商品")
        print("=" * 50)
        
        # 提取店铺名称
        shop_name = shop_url.split('shopee.sg/')[-1].split('#')[0].split('?')[0]
        print(f"📍 店铺名称: {shop_name}")
        
        all_products = []
        
        # 方法1: 尝试API
        print("\n📡 尝试API获取...")
        api_products = self.try_api_endpoints(shop_name)
        all_products.extend(api_products)
        
        # 方法2: HTML解析
        if not all_products:
            print("\n🌐 尝试HTML解析...")
            html_content = self.get_page_with_retry(shop_url)
            
            if html_content:
                html_products = self.extract_products_from_html(html_content)
                all_products.extend(html_products)
        
        # 方法3: 尝试不同的URL格式
        if not all_products:
            alternative_urls = [
                f"https://shopee.sg/{shop_name}",
                f"https://shopee.sg/shop/{shop_name}",
                f"https://shopee.sg/{shop_name}?page=0&sortBy=pop"
            ]
            
            for alt_url in alternative_urls:
                print(f"\n🔄 尝试替代URL: {alt_url}")
                html_content = self.get_page_with_retry(alt_url)
                
                if html_content:
                    html_products = self.extract_products_from_html(html_content)
                    all_products.extend(html_products)
                    
                    if html_products:
                        break
        
        # 去重
        unique_products = []
        seen_titles = set()
        for product in all_products:
            title = product['name']
            if title and title not in seen_titles:
                unique_products.append(product)
                seen_titles.add(title)
        
        print(f"\n📊 总计获取到 {len(unique_products)} 个唯一商品")
        return unique_products, shop_name
    
    def save_to_file(self, products, shop_name):
        """保存商品标题到文件"""
        if not products:
            print("❌ 没有商品数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"shopee_{shop_name}_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Shopee店铺商品标题\n")
                f.write(f"店铺: {shop_name}\n")
                f.write(f"抓取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"商品数量: {len(products)}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, product in enumerate(products, 1):
                    f.write(f"{i:3d}. {product['name']}\n")
            
            print(f"✅ 商品标题已保存到: {filename}")
            print(f"📁 文件路径: {os.path.abspath(filename)}")
            
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")

def main():
    """主函数"""
    print("🚀 高级Shopee商品标题爬虫")
    print("=" * 50)
    
    # 检查并安装依赖
    if not install_beautifulsoup():
        print("⚠️ 将使用基础解析功能")
    
    shop_url = "https://shopee.sg/sinokigo.sg#product_list"
    
    scraper = AdvancedShopeeScraper()
    products, shop_name = scraper.scrape_shop(shop_url)
    
    if products:
        scraper.save_to_file(products, shop_name)
        
        # 显示预览
        print(f"\n📋 商品标题预览 (前10个):")
        for i, product in enumerate(products[:10], 1):
            print(f"  {i:2d}. {product['name']}")
        
        if len(products) > 10:
            print(f"  ... 还有 {len(products) - 10} 个商品")
    else:
        print("❌ 未能获取到商品信息")
        print("\n💡 可能的解决方案:")
        print("1. 使用VPN更换IP地址")
        print("2. 手动访问页面确认是否需要验证")
        print("3. 尝试在浏览器中打开页面，然后保存HTML文件进行离线解析")
        print("4. 考虑使用Shopee官方API（需要申请开发者权限）")

if __name__ == "__main__":
    main()
